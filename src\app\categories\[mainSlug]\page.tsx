import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { shopApi } from '@/services/api'
import FeaturedProducts from '@/components/shop/FeaturedProducts'

// Revalidate every hour for category pages (they change less frequently than products)
export const revalidate = 3600

interface MainCategoryPageProps {
  params: { mainSlug: string }
}

// Control dynamic params - only build known routes at build time
export const dynamicParams = false

// Generate static params for all main categories
export async function generateStaticParams() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300'
    const response = await fetch(`${baseUrl}/api/shop/main-categories`, {
      // No cache during build time to get fresh data
      cache: 'no-store'
    })

    if (!response.ok) {
      console.error('Failed to fetch main categories for static params')
      return []
    }

    const data = await response.json()
    const categories = data.data || data

    return categories.map((category: any) => ({
      mainSlug: category.slug,
    }))
  } catch (error) {
    console.error('Error generating static params for main categories:', error)
    return []
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: MainCategoryPageProps): Promise<Metadata> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const mainCategories = await shopApi.getMainCategories()
    const categories = mainCategories.data || mainCategories
    const category = categories.find((cat: any) => cat.slug === params.mainSlug)

    if (!category) {
      return {
        title: 'Category Not Found',
        description: 'The requested category could not be found.',
      }
    }

    const title = `${category.name} - Premium Products | Your Store`
    const description = `Discover our ${category.name.toLowerCase()} collection. High-quality products with ${category.count || 0} items available.`
    const url = `${baseUrl}/categories/${category.slug}`
    const keywords = [category.name, 'products', 'shop', 'buy online', 'premium quality']

    return {
      title,
      description,
      keywords,
      openGraph: {
        title: `${category.name} - Premium Products`,
        description,
        url,
        siteName: 'Your Store',
        images: category.imageUrl ? [{
          url: category.imageUrl,
          alt: category.name,
          width: 800,
          height: 600
        }] : [],
        type: 'website',
        locale: 'en_US',
      },
      twitter: {
        card: 'summary_large_image',
        title: `${category.name} - Premium Products`,
        description,
        images: category.imageUrl ? [category.imageUrl] : [],
      },
      alternates: {
        canonical: url,
      },
    }
  } catch (error) {
    console.error('Error generating metadata for main category:', error)
    return {
      title: 'Category',
      description: 'Browse our product categories.',
    }
  }
}

export default async function MainCategoryPage({ params }: MainCategoryPageProps) {
  try {
    // Get main categories to find the current one
    const mainCategories = await shopApi.getMainCategories()
    const categories = mainCategories.data || mainCategories
    const currentCategory = categories.find((cat: any) => cat.slug === params.mainSlug)

    if (!currentCategory) {
      notFound()
    }

    // Get subcategories for this main category
    const subcategoriesData = await shopApi.getSubcategories(currentCategory.id)
    const subcategories = subcategoriesData.subcategories || []

    // Get some featured products from this category
    const productsData = await shopApi.getProductsByMainCategorySlug(params.mainSlug, 1, 8)
    const featuredProducts = productsData.data || []

    // Enhanced JSON-LD structured data
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'

    // Breadcrumb structured data
    const breadcrumbJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: baseUrl,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: currentCategory.name,
          item: `${baseUrl}/categories/${currentCategory.slug}`,
        },
      ],
    }

    // Collection page structured data
    const collectionJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: currentCategory.name,
      description: `Browse our ${currentCategory.name.toLowerCase()} collection`,
      url: `${baseUrl}/categories/${currentCategory.slug}`,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: currentCategory.count || 0,
        itemListElement: subcategories.map((subcat: any, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Thing',
            name: subcat.name,
            url: `${baseUrl}/categories/${currentCategory.slug}/${subcat.slug}`,
          },
        })),
      },
    }

    // Organization structured data
    const organizationJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Your Store',
      url: baseUrl,
      logo: `${baseUrl}/logo.png`,
    }

    return (
      <>
        {/* JSON-LD structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbJsonLd) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(collectionJsonLd) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }}
        />

        <div className="min-h-screen bg-white">
          {/* Hero Section */}
          <div className="relative bg-gradient-to-r from-gray-50 to-gray-100 py-16">
            <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
              <div className="max-w-4xl">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                  {currentCategory.name}
                </h1>
                <p className="text-xl text-gray-600 mb-6">
                  Discover our premium {currentCategory.name.toLowerCase()} collection with {currentCategory.count || 0} products
                </p>
                <nav className="text-sm text-gray-500">
                  <Link href="/" className="hover:text-gray-700">Home</Link>
                  <span className="mx-2">/</span>
                  <span>Categories</span>
                  <span className="mx-2">/</span>
                  <span className="text-gray-900">{currentCategory.name}</span>
                </nav>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
            {/* Subcategories Grid */}
            {subcategories.length > 0 && (
              <section className="mb-16">
                <h2 className="text-2xl font-semibold text-gray-900 mb-8">Browse by Category</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {subcategories.map((subcategory: any) => (
                    <Link
                      key={subcategory.id}
                      href={`/categories/${params.mainSlug}/${subcategory.slug}`}
                      className="group bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                    >
                      <div className="aspect-square relative overflow-hidden rounded-t-lg">
                        {subcategory.imageUrl ? (
                          <Image
                            src={subcategory.imageUrl}
                            alt={subcategory.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                            <span className="text-gray-400 text-4xl">📦</span>
                          </div>
                        )}
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900 group-hover:text-main-color transition-colors">
                          {subcategory.name}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {subcategory.count || 0} products
                        </p>
                      </div>
                    </Link>
                  ))}
                </div>
              </section>
            )}

            {/* Featured Products */}
            {featuredProducts.length > 0 && (
              <section>
                <div className="flex justify-between items-center mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900">Featured Products</h2>
                  <Link
                    href={`/categories/${params.mainSlug}/products`}
                    className="text-main-color hover:underline font-medium"
                  >
                    View All Products →
                  </Link>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {featuredProducts.slice(0, 8).map((product: any) => (
                    <Link
                      key={product.id}
                      href={`/products/${product.slug}`}
                      className="group bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                    >
                      <div className="aspect-square relative overflow-hidden rounded-t-lg">
                        {product.imageUrl ? (
                          <Image
                            src={product.imageUrl}
                            alt={product.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                            <span className="text-gray-400 text-4xl">📦</span>
                          </div>
                        )}
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900 group-hover:text-main-color transition-colors line-clamp-2">
                          {product.name}
                        </h3>
                        <div className="flex items-center gap-2 mt-2">
                          {product.salePrice ? (
                            <>
                              <span className="text-lg font-semibold text-main-color">
                                ${product.salePrice.toFixed(2)}
                              </span>
                              <span className="text-sm text-gray-500 line-through">
                                ${product.price.toFixed(2)}
                              </span>
                            </>
                          ) : (
                            <span className="text-lg font-semibold text-gray-900">
                              ${product.price.toFixed(2)}
                            </span>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>
              </section>
            )}
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading main category page:', error)
    notFound()
  }
}
