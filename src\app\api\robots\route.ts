import { NextResponse } from 'next/server'

export async function GET() {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
  
  const robotsTxt = `User-agent: *
Allow: /

# Allow all product and category pages
Allow: /products/
Allow: /categories/

# Allow static pages
Allow: /about
Allow: /contact
Allow: /blog

# Disallow admin and API routes
Disallow: /admin/
Disallow: /api/
Disallow: /login
Disallow: /register
Disallow: /profile
Disallow: /cart
Disallow: /checkout

# Disallow test and development pages
Disallow: /test-product/
Disallow: /direct-test/

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml`

  return new NextResponse(robotsTxt, {
    headers: {
      'Content-Type': 'text/plain',
      'Cache-Control': 'public, max-age=86400', // 24 hours
    },
  })
}
