/** @type {import('next').NextConfig} */
const nextConfig = {
  // Disable ESLint during build for deployment
  eslint: {
    ignoreDuringBuilds: true,
  },

  reactStrictMode: true,

  // Enable experimental features for better SEO
  experimental: {
    // Enable partial prerendering for better performance
    ppr: false,
  },

  // Configure rewrite rules for SEO-friendly URLs
  async rewrites() {
    return [
      {
        source: '/robots.txt',
        destination: '/api/robots',
      },
    ]
  },

  // Configure headers for better SEO and performance
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block',
          },
        ],
      },
      {
        source: '/sitemap.xml',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400, s-maxage=86400', // 24 hours
          },
        ],
      },
    ]
  },
  images: {
    domains: [
      'images.unsplash.com',
      'example.com',
      'localhost',
      'via.placeholder.com',
      'picsum.photos',
      'placehold.co',
      'img.freepik.com',
      'bostondirecthealth.com',
      'i0.wp.com',
      'www.elementis.com',
      'th.bing.com',
      'media.istockphoto.com',
      'm.media-amazon.com',
      'loremflickr.com',
      'random-image-pepebigotes.vercel.app',
      'as2.ftcdn.net',
      'as1.ftcdn.net',
      'as3.ftcdn.net',
      'cdn.pixabay.com',
      'images.pexels.com',
      'st.depositphotos.com',
      'thumbs.dreamstime.com',
      'cdn.shopify.com',
      'www.sephora.com',
      'www.ulta.com',
      'www.amazon.com',
      's3.amazonaws.com',
      'cloudfront.net'
    ],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      }
    ],
    // Disable image optimization for certain domains
    unoptimized: process.env.NODE_ENV === 'development',
    // Increase the image cache TTL
    minimumCacheTTL: 60,
    // Configure image formats
    formats: ['image/webp'],
    // Disable dangerouslyAllowSVG for security
    dangerouslyAllowSVG: false,
    // Configure content security policy
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  // Add experimental features (app directory is stable in Next.js 14+)
  // experimental: {
  //   appDir: true,
  // },
}

module.exports = nextConfig;

