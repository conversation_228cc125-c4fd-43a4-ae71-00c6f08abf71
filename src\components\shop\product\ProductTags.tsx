"use client";

import Link from 'next/link';
import { FiTag } from 'react-icons/fi';

interface ProductTag {
  id: number;
  name: string;
  slug: string;
}

interface ProductTagsProps {
  tags: ProductTag[];
  showTitle?: boolean;
  className?: string;
}

const ProductTags = ({ tags, showTitle = true, className = '' }: ProductTagsProps) => {
  if (!tags || tags.length === 0) {
    return null;
  }

  return (
    <div className={className}>
      {showTitle && (
        <h3 className="font-medium text-gray-800 mb-2 flex items-center gap-2">
          <FiTag size={16} /> Tags
        </h3>
      )}
      <div className="flex flex-wrap gap-2">
        {tags.map(tag => (
          <Link
            key={tag.id}
            href={`/tags/${tag.slug}`}
            className="inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm hover:bg-blue-200 transition-colors"
            title={`View all products tagged with ${tag.name}`}
          >
            #{tag.name}
          </Link>
        ))}
      </div>
    </div>
  );
};

export default ProductTags;
