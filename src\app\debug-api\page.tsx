import { shopApi } from '@/services/api'

export default async function DebugApiPage() {
  try {
    console.log('🔍 Testing Shop API endpoints...')
    
    // Test main categories
    const mainCategories = await shopApi.getMainCategories()
    console.log('Main categories:', mainCategories)
    
    // Test product names
    const productNames = await shopApi.getAllProductNames()
    console.log('Product names:', productNames)
    
    // Test a specific product if available
    let testProduct = null
    if (productNames.data && productNames.data.length > 0) {
      const firstProductSlug = productNames.data[0].slug
      testProduct = await shopApi.getProductBySlug(firstProductSlug)
      console.log('Test product:', testProduct)
    }
    
    return (
      <div className="min-h-screen bg-white p-8">
        <h1 className="text-3xl font-bold mb-8">API Debug Page</h1>
        
        <div className="space-y-8">
          <section>
            <h2 className="text-2xl font-semibold mb-4">Main Categories</h2>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto">
              {JSON.stringify(mainCategories, null, 2)}
            </pre>
          </section>
          
          <section>
            <h2 className="text-2xl font-semibold mb-4">Product Names</h2>
            <pre className="bg-gray-100 p-4 rounded-lg overflow-auto">
              {JSON.stringify(productNames, null, 2)}
            </pre>
          </section>
          
          {testProduct && (
            <section>
              <h2 className="text-2xl font-semibold mb-4">Test Product</h2>
              <pre className="bg-gray-100 p-4 rounded-lg overflow-auto">
                {JSON.stringify(testProduct, null, 2)}
              </pre>
            </section>
          )}
        </div>
      </div>
    )
  } catch (error) {
    console.error('API Debug Error:', error)
    
    return (
      <div className="min-h-screen bg-white p-8">
        <h1 className="text-3xl font-bold mb-8 text-red-600">API Debug Error</h1>
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <p className="font-bold">Error testing API endpoints:</p>
          <p>{error instanceof Error ? error.message : 'Unknown error'}</p>
        </div>
      </div>
    )
  }
}
