"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { FiChevronRight } from "react-icons/fi";
import { Category, SubCategory } from "@/data/hierarchicalCategories";

interface CategoryHierarchyProps {
  categories: Category[];
  onSelectCategory: (categoryId: number | null) => void;
  onSelectSubcategory: (subcategoryId: number | null) => void;
  selectedCategory: number | null;
  selectedSubcategory: number | null;
}

const CategoryHierarchy = ({
  categories,
  onSelectCategory,
  onSelectSubcategory,
  selectedCategory,
  selectedSubcategory
}: CategoryHierarchyProps) => {
  // Get the selected category object
  const currentCategory = selectedCategory
    ? categories.find(cat => cat.id === selectedCategory)
    : null;

  // Get the selected subcategory object
  const currentSubcategory = selectedSubcategory && currentCategory
    ? currentCategory.subcategories.find(subcat => subcat.id === selectedSubcategory)
    : null;

  return (
    <div className="mb-12">
      {/* Breadcrumb Navigation */}
      <div className="flex items-center text-sm text-gray-600 mb-6">
        <Link href="/shop" className="hover:text-main-color">
          Shop
        </Link>

        {currentCategory && (
          <>
            <FiChevronRight className="mx-2" />
            <span
              className={`${selectedSubcategory ? 'hover:text-main-color cursor-pointer' : 'font-medium text-gray-800'}`}
              onClick={() => {
                if (selectedSubcategory) {
                  onSelectSubcategory(null);
                }
              }}
            >
              {currentCategory.name}
            </span>
          </>
        )}

        {currentSubcategory && (
          <>
            <FiChevronRight className="mx-2" />
            <span className="font-medium text-gray-800">
              {currentSubcategory.name}
            </span>
          </>
        )}
      </div>

      {/* Main Categories */}
      {!selectedCategory && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          {categories.map((category) => (
            <div
              key={category.id}
              className="group relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
              onClick={() => onSelectCategory(category.id)}
            >
              <div className="relative h-64 w-full overflow-hidden">
                <Image
                  src={category.imageUrl}
                  alt={category.name}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
              </div>
              <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                <h3 className="text-xl font-medium">{category.name}</h3>
                <p className="text-sm opacity-80">{category.count} products</p>
              </div>
              <div className="absolute inset-0 flex items-center justify-center bg-main-color/0 group-hover:bg-main-color/20 transition-all duration-300">
                <button className="bg-white text-main-color px-4 py-2 rounded-full font-medium transform translate-y-10 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                  View Category
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Subcategories (when a main category is selected) */}
      {selectedCategory && !selectedSubcategory && currentCategory && (
        <>
          <h2 className="text-2xl font-medium text-gray-800 mb-6">{currentCategory.name}</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {currentCategory.subcategories.map((subcategory) => (
              <div
                key={subcategory.id}
                className="group relative overflow-hidden rounded-lg shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer"
                onClick={() => onSelectSubcategory(subcategory.id)}
              >
                <div className="relative h-48 w-full overflow-hidden">
                  <Image
                    src={subcategory.imageUrl}
                    alt={subcategory.name}
                    fill
                    className="object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                </div>
                <div className="absolute bottom-0 left-0 right-0 p-4 text-white">
                  <h3 className="text-lg font-medium">{subcategory.name}</h3>
                  <p className="text-sm opacity-80">{subcategory.count} products</p>
                </div>
                <div className="absolute inset-0 flex items-center justify-center bg-main-color/0 group-hover:bg-main-color/20 transition-all duration-300">
                  <button className="bg-white text-main-color px-4 py-2 rounded-full font-medium transform translate-y-10 opacity-0 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-300">
                    View Products
                  </button>
                </div>
              </div>
            ))}
          </div>
        </>
      )}

      {/* Selected Subcategory Title (when a subcategory is selected) */}
      {selectedSubcategory && currentSubcategory && (
        <h2 className="text-2xl font-medium text-gray-800 mb-6">{currentSubcategory.name}</h2>
      )}
    </div>
  );
};

export default CategoryHierarchy;
