import type { Metadata } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import LayoutWrapper from "@/components/LayoutWrapper";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    template: "%s | CocoJojo",
    default: "CocoJojo",
  },
  description: "At Organic Pure Oil, our team believes in high quality beauty products that harnesses the healing powers of Witch Hazel Rose Toner for Skinpure, natural ingredients to deliver honest beauty products that produce real results, enhance your well-being, and sustain the Earth. No additives, no funny business. Just 100% pure, organic oils.",
  keywords: "organic oils, beauty products, skincare, natural ingredients, witch hazel, rose toner, pure oils",
  authors: [{ name: "CocoJojo Team" }],
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://cocojojo.com",
    siteName: "CocoJojo",
    title: "CocoJojo - Premium Organic Beauty Products",
    description: "High quality beauty products with pure, natural ingredients for real results and well-being.",
  },
  twitter: {
    card: "summary_large_image",
    title: "CocoJojo - Premium Organic Beauty Products",
    description: "High quality beauty products with pure, natural ingredients for real results and well-being.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <LayoutWrapper>
          {children}
        </LayoutWrapper>
      </body>
    </html>
  );
}
