// API service for the application

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_PATH_PREFIX = process.env.NEXT_PUBLIC_API_PATH_PREFIX || '/api/store';

// Shop API base URL for customer-facing endpoints
const SHOP_API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const SHOP_API_PREFIX = '/api/shop';

// Log the API configuration to help with debugging
console.log('API Base URL:', API_BASE_URL);
console.log('API Path Prefix:', API_PATH_PREFIX);

// Category types
export interface Category {
  id?: number;
  name: string;
  slug: string;
  description: string;
  mainCategoryId?: number;
  imageUrl?: string;
}

// Main Category types
export interface MainCategory {
  id?: number;
  name: string;
  slug: string;
  createdAt?: string;
  updatedAt?: string;
  categories?: Category[];
  categoryIds?: number[];
  imageUrl?: string;
}

// Product types
export enum StockStatus {
  IN_STOCK = 'IN_STOCK',
  OUT_OF_STOCK = 'OUT_OF_STOCK',
  ON_BACKORDER = 'ON_BACKORDER'
}

export enum TaxStatus {
  TAXABLE = 'TAXABLE',
  SHIPPING = 'SHIPPING',
  NONE = 'NONE'
}

export enum TaxClass {
  STANDARD = 'STANDARD',
  REDUCED = 'REDUCED',
  ZERO = 'ZERO'
}

export enum ProductType {
  SIMPLE = 'SIMPLE',
  VARIABLE = 'VARIABLE',
  GROUPED = 'GROUPED',
  EXTERNAL = 'EXTERNAL'
}

export enum AccessLevel {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  PROTECTED = 'PROTECTED'
}

export interface ProductImage {
  id?: number;
  url: string;
  position: number;
  productId?: number;
}

export interface ProductListing {
  id?: number;
  title: string;
  content: string;
  productId?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface ProductSectionItem {
  id: number;
  productId: number;
  position: number;
  product: {
    id: number;
    imageUrl: string;
    name: string;
    price: number;
    salePrice?: number;
    sku: string;
    stockStatus: StockStatus;
    createdAt?: string;

  };
}

export interface ProductSection {
  id?: number;
  name: string;
  position: number;
  createdAt?: string;
  updatedAt?: string;
  items?: ProductSectionItem[];
}

export interface Product {
  id?: number;
  sku: string;
  name: string;
  description: string;
  shortDescription: string;
  price: string;
  salePrice?: string;
  saleStart?: string;
  saleEnd?: string;
  stockQuantity: number | null;
  stockStatus: StockStatus;
  taxStatus?: TaxStatus;
  taxClass?: TaxClass;
  type?: ProductType;
  access?: AccessLevel;
  password?: string;
  tags?: string[];
  createdAt?: string;
  updatedAt?: string;
  images: ProductImage[];
  categories?: Category[];
  categoryIds?: number[];
  attributes?: ProductAttribute[];
  listings?: ProductListing[];
}

export interface ProductAttribute {
  id?: number;
  name: string;
}

export interface AttributeValue {
  id?: number;
  value: string;
  productAttributeId?: number;
  delete?: boolean;
}

export interface ProductAttributeWithValues {
  id?: number;
  attributeId: number;
  values: AttributeValue[];
}

export interface ProductVariant {
  id?: number;
  sku: string;
  price: string;
  salePrice?: string;
  saleStart?: string;
  saleEnd?: string;
  stockQuantity?: number;
  stockStatus: StockStatus;
  attributeValueIds: number[];
  images: ProductImage[];
  delete?: boolean;
}

export interface GroupedProductData {
  sku: string;
  name: string;
  description: string;
  shortDescription: string;
  price: string;
  salePrice?: string;
  stockStatus: StockStatus;
  taxStatus?: TaxStatus;
  taxClass?: TaxClass;
  type: ProductType.GROUPED;
  access?: AccessLevel;
  password?: string;
  tags?: string[];
  categoryIds: number[];
  images: ProductImage[];
  productAttributes: ProductAttributeWithValues[];
  variants: ProductVariant[];
  listings?: ProductListing[];
}

export interface GroupedProductUpdateData {
  sku?: string;
  name?: string;
  description?: string;
  shortDescription?: string;
  price?: string;
  salePrice?: string;
  saleStart?: string;
  saleEnd?: string;
  stockQuantity?: number;
  stockStatus?: StockStatus;
  taxStatus?: TaxStatus;
  taxClass?: TaxClass;
  access?: AccessLevel;
  password?: string;
  categoryIds?: number[];
  tags?: string[];
  images?: ProductImage[];
  productAttributes?: ProductAttributeWithValues[];
  variants?: ProductVariant[];
  deleteVariantIds?: number[];
  deleteProductAttributeIds?: number[];
  deleteAttributeValueIds?: number[];
  listings?: ProductListing[];
  deleteListingIds?: number[];
}


// Helper function to handle API responses
const handleResponse = async <T>(response: Response): Promise<T> => {
  // Log the response status and URL for debugging
  console.log(`API Response: ${response.status} ${response.url}`);
  console.log(`API Response Headers:`, Object.fromEntries(response.headers.entries()));

  if (!response.ok) {
    // Try to get error details from the response
    let errorMessage = `Error: ${response.status} ${response.statusText}`;
    let errorDetails = '';

    try {
      // Only try to parse JSON if the content type is JSON
      const contentType = response.headers.get('content-type');
      if (contentType && contentType.includes('application/json')) {
        const errorData = await response.json();
        console.error('API Error Response (JSON):', errorData);
        errorMessage = errorData.message || errorMessage;
        errorDetails = JSON.stringify(errorData, null, 2);
      } else {
        // For non-JSON responses, try to get the text
        const text = await response.text();
        console.error('API Error Response (Text):', text);
        if (text) {
          errorMessage = `${errorMessage} - ${text}`;
          errorDetails = text;
        }
      }
    } catch (e) {
      console.error('Error parsing error response:', e);
      // If we can't parse the error response, use the default message
    }

    console.error(`API Error: ${errorMessage}`);
    if (errorDetails) {
      console.error(`API Error Details: ${errorDetails}`);
    }

    throw new Error(errorMessage);
  }

  try {
    const jsonData = await response.json();
    console.log('Parsed JSON response:', jsonData);

    // Check if the URL contains endpoints that should return arrays
    const url = response.url.toLowerCase();
    const shouldBeArray =
      url.includes('/categories') && !url.includes('/categories/') ||
      url.includes('/products') && !url.includes('/products/') ||
      url.includes('/categories/') && url.includes('/products');

    // If we expect an array but got something else
    if (shouldBeArray && !Array.isArray(jsonData)) {
      console.warn(`Expected array for ${url} but got:`, typeof jsonData);
      return ([] as unknown) as T;
    }

    // If we got an array as expected
    if (Array.isArray(jsonData)) {
      return jsonData as T;
    }

    return jsonData;
  } catch (e) {
    console.error('Error parsing JSON response:', e);

    // Check if the URL contains endpoints that should return arrays
    const url = response.url.toLowerCase();
    const shouldBeArray =
      url.includes('/categories') && !url.includes('/categories/') ||
      url.includes('/products') && !url.includes('/products/') ||
      url.includes('/categories/') && url.includes('/products');

    // Return appropriate default value based on expected type
    if (shouldBeArray) {
      return ([] as unknown) as T;
    }

    return {} as T;
  }
};

// Category API service
export const categoryApi = {
  // Get all categories
  getAll: async (): Promise<Category[]> => {
    try {
      console.log('Fetching all categories...');

      // Use a proxy URL to avoid CORS issues
      // This can be a Next.js API route that forwards the request to your backend
      const proxyUrl = '/api/store/categories';

      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<Category[]>(response);
    } catch (error) {
      console.error('Failed to fetch categories:', error);
      // Re-throw the error to be handled by the caller
      throw error;
    }
  },

  // Get a category by ID
  getById: async (id: number): Promise<Category> => {
    try {
      console.log(`Fetching category with ID ${id}...`);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/categories/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<Category>(response);
    } catch (error) {
      console.error(`Failed to fetch category with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new category
  create: async (category: Category): Promise<Category> => {
    try {
      console.log('Creating new category:', category);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/store/categories';

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(category),
      });
      return await handleResponse<Category>(response);
    } catch (error) {
      console.error('Failed to create category:', error);
      throw error;
    }
  },

  // Update a category
  update: async (id: number, category: Partial<Category>): Promise<Category> => {
    try {
      console.log(`Updating category with ID ${id}:`, category);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/categories/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(category),
      });
      return await handleResponse<Category>(response);
    } catch (error) {
      console.error(`Failed to update category with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete a category
  delete: async (id: number): Promise<Category> => {
    try {
      console.log(`Deleting category with ID ${id}...`);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/categories/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'DELETE',
      });
      return await handleResponse<Category>(response);
    } catch (error) {
      console.error(`Failed to delete category with ID ${id}:`, error);
      throw error;
    }
  },
};

// Main Category API service
export const mainCategoryApi = {
  // Get all main categories
  getAll: async (): Promise<MainCategory[]> => {
    try {
      console.log('Fetching all main categories...');

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/store/main-categories';

      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<MainCategory[]>(response);
    } catch (error) {
      console.error('Failed to fetch main categories:', error);
      throw error;
    }
  },

  // Get a main category by ID
  getById: async (id: number): Promise<MainCategory> => {
    try {
      console.log(`Fetching main category with ID ${id}...`);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/main-categories/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<MainCategory>(response);
    } catch (error) {
      console.error(`Failed to fetch main category with ID ${id}:`, error);
      throw error;
    }
  },

  // Create a new main category
  create: async (mainCategory: MainCategory): Promise<MainCategory> => {
    try {
      console.log('Creating new main category:', mainCategory);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/store/main-categories';

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mainCategory),
      });
      return await handleResponse<MainCategory>(response);
    } catch (error) {
      console.error('Failed to create main category:', error);
      throw error;
    }
  },

  // Update a main category
  update: async (id: number, mainCategory: Partial<MainCategory>): Promise<MainCategory> => {
    try {
      console.log(`Updating main category with ID ${id}:`, mainCategory);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/main-categories/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(mainCategory),
      });
      return await handleResponse<MainCategory>(response);
    } catch (error) {
      console.error(`Failed to update main category with ID ${id}:`, error);
      throw error;
    }
  },

  // Add categories to a main category
  addCategories: async (id: number, categoryIds: number[]): Promise<MainCategory> => {
    try {
      console.log(`Adding categories to main category with ID ${id}:`, categoryIds);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/main-categories/${id}/categories`;

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ categoryIds }),
      });
      return await handleResponse<MainCategory>(response);
    } catch (error) {
      console.error(`Failed to add categories to main category with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete a main category
  delete: async (id: number): Promise<MainCategory> => {
    try {
      console.log(`Deleting main category with ID ${id}...`);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/main-categories/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'DELETE',
      });
      return await handleResponse<MainCategory>(response);
    } catch (error) {
      console.error(`Failed to delete main category with ID ${id}:`, error);
      throw error;
    }
  },
};

// Product API service
export const productApi = {
  // Get all products
  getAll: async (): Promise<Product[]> => {
    try {
      console.log('Fetching all products...');

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/store/products';

      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<Product[]>(response);
    } catch (error) {
      console.error('Failed to fetch products:', error);
      throw error;
    }
  },

  // Get a product by ID
  getById: async (id: number): Promise<Product> => {
    try {
      console.log(`Fetching product with ID ${id}...`);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/products/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<Product>(response);
    } catch (error) {
      console.error(`Failed to fetch product with ID ${id}:`, error);
      throw error;
    }
  },

  // Get products by category ID
  getByCategory: async (categoryId: number): Promise<Product[]> => {
    try {
      console.log(`API service - Fetching products for category ID ${categoryId}...`);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/categories/${categoryId}/products`;
      console.log(`API service - Using proxy URL: ${proxyUrl}`);

      const response = await fetch(proxyUrl, {
        method: 'GET',
      });

      console.log(`API service - Response status for category ${categoryId} products:`, response.status);

      const data = await handleResponse<Product[]>(response);
      console.log(`API service - Parsed data for category ${categoryId} products:`, data);

      // Ensure we always return an array
      if (!Array.isArray(data)) {
        console.error(`API service - Expected array but got:`, data);
        return [];
      }

      return data;
    } catch (error) {
      console.error(`Failed to fetch products for category ID ${categoryId}:`, error);
      throw error;
    }
  },

  // Create a new product
  create: async (product: Partial<Product>): Promise<Product> => {
    try {
      console.log('Creating new product:', product);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = '/api/store/products';

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(product),
      });
      return await handleResponse<Product>(response);
    } catch (error) {
      console.error('Failed to create product:', error);
      throw error;
    }
  },

  // Create a new product for a specific category
  createForCategory: async (categoryId: number, product: Partial<Product>): Promise<Product> => {
    try {
      console.log(`Creating new product for category ID ${categoryId}:`, product);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/categories/${categoryId}/products`;

      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(product),
      });
      return await handleResponse<Product>(response);
    } catch (error) {
      console.error(`Failed to create product for category ID ${categoryId}:`, error);
      throw error;
    }
  },

  // Update a product
  update: async (id: number, product: Partial<Product>): Promise<Product> => {
    try {
      console.log(`API service - Updating product with ID ${id}:`, product);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/products/${id}`;
      console.log(`API service - Using proxy URL: ${proxyUrl}`);

      const response = await fetch(proxyUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(product),
      });

      console.log(`API service - Response status for updating product ${id}:`, response.status);

      const updatedProduct = await handleResponse<Product>(response);
      console.log(`API service - Updated product data received:`, updatedProduct);

      return updatedProduct;
    } catch (error) {
      console.error(`Failed to update product with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete a product
  delete: async (id: number): Promise<Product> => {
    try {
      console.log(`Deleting product with ID ${id}...`);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/products/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'DELETE',
      });
      return await handleResponse<Product>(response);
    } catch (error) {
      console.error(`Failed to delete product with ID ${id}:`, error);
      throw error;
    }
  },
};

export const groupedProductApi = {
  // Attribute management
  getAttributes: async (): Promise<ProductAttribute[]> => {
    const response = await fetch('/api/store/grouped-products/attributes');
    return handleResponse<ProductAttribute[]>(response);
  },

  createAttribute: async (attribute: Omit<ProductAttribute, 'id'>): Promise<ProductAttribute> => {
    // Create an attribute with empty values array as per the new API format
    const attributeWithValues = {
      name: attribute.name,
    };

    const response = await fetch('/api/store/grouped-products/attributes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(attributeWithValues),
    });
    return handleResponse<ProductAttribute>(response);
  },

  // Grouped product management
  getById: async (id: number): Promise<Product> => {
    try {
      console.log(`Fetching grouped product with ID ${id}...`);

      // Use a proxy URL to avoid CORS issues
      const proxyUrl = `/api/store/grouped-products/${id}`;

      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<Product>(response);
    } catch (error) {
      console.error(`Failed to fetch grouped product with ID ${id}:`, error);
      throw error;
    }
  },

  createGroupedProduct: async (product: GroupedProductData): Promise<Product> => {
    console.log('API Service - Creating grouped product with body:', JSON.stringify(product, null, 2));

    const response = await fetch('/api/store/grouped-products', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(product),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Service - Error creating grouped product:', errorText);
      throw new Error(`Failed to create grouped product: ${errorText}`);
    }

    return handleResponse<Product>(response);
  },

  updateGroupedProduct: async (id: number, product: GroupedProductUpdateData): Promise<Product> => {
    console.log('API Service - Updating grouped product with body:', JSON.stringify(product, null, 2));

    const response = await fetch(`/api/store/grouped-products/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(product),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Service - Error updating grouped product:', errorText);
      throw new Error(`Failed to update grouped product: ${errorText}`);
    }

    return handleResponse<Product>(response);
  },

  updateGroupedProductBase: async (id: number, baseData: Partial<GroupedProductUpdateData>): Promise<Product> => {
    console.log('API Service - Updating grouped product base with body:', JSON.stringify(baseData, null, 2));

    const response = await fetch(`/api/store/grouped-products/${id}/base`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(baseData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Service - Error updating grouped product base:', errorText);
      throw new Error(`Failed to update grouped product base: ${errorText}`);
    }

    return handleResponse<Product>(response);
  },

  updateGroupedProductVariant: async (productId: number, variantId: number, variantData: Partial<ProductVariant>): Promise<Product> => {
    console.log('API Service - Updating grouped product variant with body:', JSON.stringify(variantData, null, 2));

    const response = await fetch(`/api/store/grouped-products/${productId}/variants/${variantId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(variantData),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('API Service - Error updating grouped product variant:', errorText);
      throw new Error(`Failed to update grouped product variant: ${errorText}`);
    }

    return handleResponse<Product>(response);
  },

  deleteGroupedProduct: async (id: number): Promise<Product> => {
    try {
      console.log(`Deleting grouped product with ID ${id}...`);

      const response = await fetch(`/api/store/grouped-products/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API Service - Error deleting grouped product with ID ${id}:`, errorText);
        throw new Error(`Failed to delete grouped product: ${errorText}`);
      }

      return await handleResponse<Product>(response);
    } catch (error) {
      console.error(`Failed to delete grouped product with ID ${id}:`, error);
      throw error;
    }
  },
};

// Listings API service
export const listingsApi = {
  // Get all listings for a product
  getByProductId: async (productId: number): Promise<ProductListing[]> => {
    try {
      console.log(`Fetching listings for product ID ${productId}...`);
      const proxyUrl = `/api/store/listings/product/${productId}`;
      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<ProductListing[]>(response);
    } catch (error) {
      console.error(`Failed to fetch listings for product ID ${productId}:`, error);
      throw error;
    }
  },

  // Create a new listing
  create: async (listing: Omit<ProductListing, 'id'>): Promise<ProductListing> => {
    try {
      console.log('Creating new listing:', listing);
      const proxyUrl = '/api/store/listings';
      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(listing),
      });
      return await handleResponse<ProductListing>(response);
    } catch (error) {
      console.error('Failed to create listing:', error);
      throw error;
    }
  },

  // Update a listing
  update: async (id: number, listing: Partial<ProductListing>): Promise<ProductListing> => {
    try {
      console.log(`Updating listing with ID ${id}:`, listing);
      const proxyUrl = `/api/store/listings/${id}`;
      const response = await fetch(proxyUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(listing),
      });
      return await handleResponse<ProductListing>(response);
    } catch (error) {
      console.error(`Failed to update listing with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete a listing
  delete: async (id: number): Promise<ProductListing> => {
    try {
      console.log(`Deleting listing with ID ${id}...`);
      const proxyUrl = `/api/store/listings/${id}`;
      const response = await fetch(proxyUrl, {
        method: 'DELETE',
      });
      return await handleResponse<ProductListing>(response);
    } catch (error) {
      console.error(`Failed to delete listing with ID ${id}:`, error);
      throw error;
    }
  },
};

// Product Section API service
export const productSectionApi = {
  // Get all product sections
  getAll: async (): Promise<ProductSection[]> => {
    try {
      console.log('Fetching all product sections...');
      const proxyUrl = '/api/store/product-sections';
      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<ProductSection[]>(response);
    } catch (error) {
      console.error('Failed to fetch product sections:', error);
      throw error;
    }
  },

  // Get a product section by ID
  getById: async (id: number): Promise<ProductSection> => {
    try {
      console.log(`Fetching product section with ID ${id}...`);
      const proxyUrl = `/api/store/product-sections/${id}`;
      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<ProductSection>(response);
    } catch (error) {
      console.error(`Failed to fetch product section with ID ${id}:`, error);
      throw error;
    }
  },

  // Get a product section by position
  getByPosition: async (position: number): Promise<ProductSection> => {
    try {
      console.log(`Fetching product section with position ${position}...`);
      const proxyUrl = `/api/store/product-sections/position/${position}`;
      const response = await fetch(proxyUrl, {
        method: 'GET',
      });
      return await handleResponse<ProductSection>(response);
    } catch (error) {
      console.error(`Failed to fetch product section with position ${position}:`, error);
      throw error;
    }
  },

  // Create a new product section
  create: async (section: { name: string; position: number; productIds?: number[] }): Promise<ProductSection> => {
    try {
      console.log('Creating new product section:', section);
      const proxyUrl = '/api/store/product-sections';
      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(section),
      });
      return await handleResponse<ProductSection>(response);
    } catch (error) {
      console.error('Failed to create product section:', error);
      throw error;
    }
  },

  // Update a product section
  update: async (id: number, section: { name?: string; position?: number }): Promise<ProductSection> => {
    try {
      console.log(`Updating product section with ID ${id}:`, section);
      const proxyUrl = `/api/store/product-sections/${id}`;
      const response = await fetch(proxyUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(section),
      });
      return await handleResponse<ProductSection>(response);
    } catch (error) {
      console.error(`Failed to update product section with ID ${id}:`, error);
      throw error;
    }
  },

  // Delete a product section
  delete: async (id: number): Promise<void> => {
    try {
      console.log(`Deleting product section with ID ${id}...`);
      const proxyUrl = `/api/store/product-sections/${id}`;
      const response = await fetch(proxyUrl, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error(`Failed to delete product section with ID ${id}`);
      }
    } catch (error) {
      console.error(`Failed to delete product section with ID ${id}:`, error);
      throw error;
    }
  },

  // Add a product to a section
  addProduct: async (sectionId: number, productId: number, position: number): Promise<ProductSection> => {
    try {
      console.log(`Adding product ID ${productId} to section ID ${sectionId} at position ${position}...`);
      const proxyUrl = `/api/store/product-sections/${sectionId}/products`;
      const response = await fetch(proxyUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ productId, position }),
      });
      return await handleResponse<ProductSection>(response);
    } catch (error) {
      console.error(`Failed to add product ID ${productId} to section ID ${sectionId}:`, error);
      throw error;
    }
  },

  // Remove a product from a section
  removeProduct: async (sectionId: number, itemId: number): Promise<ProductSection> => {
    try {
      console.log(`Removing item ID ${itemId} from section ID ${sectionId}...`);
      const proxyUrl = `/api/store/product-sections/${sectionId}/products/${itemId}`;
      const response = await fetch(proxyUrl, {
        method: 'DELETE',
      });
      return await handleResponse<ProductSection>(response);
    } catch (error) {
      console.error(`Failed to remove item ID ${itemId} from section ID ${sectionId}:`, error);
      throw error;
    }
  },
};

// Shop API service for customer-facing endpoints
export const shopApi = {
  // Get all main categories with product counts
  getMainCategories: async (): Promise<any[]> => {
    try {
      console.log('Fetching main categories from shop API...');
      const response = await fetch(`${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/main-categories`, {
        next: { revalidate: 86400 } // 24 hours cache
      });
      return await handleResponse<any[]>(response);
    } catch (error) {
      console.error('Failed to fetch main categories:', error);
      throw error;
    }
  },

  // Get subcategories of a main category
  getSubcategories: async (mainId: number): Promise<any> => {
    try {
      console.log(`Fetching subcategories for main category ${mainId}...`);
      const response = await fetch(`${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/main-categories/${mainId}/categories`, {
        next: { revalidate: 86400 } // 24 hours cache
      });
      return await handleResponse<any>(response);
    } catch (error) {
      console.error(`Failed to fetch subcategories for main category ${mainId}:`, error);
      throw error;
    }
  },

  // Get products by category with pagination
  getProductsByCategory: async (catId: number, page: number = 1, limit: number = 20): Promise<any> => {
    try {
      console.log(`Fetching products for category ${catId}, page ${page}...`);
      const response = await fetch(`${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/categories/${catId}/products?page=${page}&limit=${limit}`, {
        next: { revalidate: 86400 } // 24 hours cache
      });
      return await handleResponse<any>(response);
    } catch (error) {
      console.error(`Failed to fetch products for category ${catId}:`, error);
      throw error;
    }
  },

  // Get products by main category slug with pagination
  getProductsByMainCategorySlug: async (slug: string, page: number = 1, limit: number = 20): Promise<any> => {
    try {
      console.log(`Fetching products for main category slug ${slug}, page ${page}...`);
      const response = await fetch(`${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/main-categories/slug/${slug}/products?page=${page}&limit=${limit}`, {
        next: { revalidate: 86400 } // 24 hours cache
      });
      return await handleResponse<any>(response);
    } catch (error) {
      console.error(`Failed to fetch products for main category slug ${slug}:`, error);
      throw error;
    }
  },

  // Get products by category slug with pagination
  getProductsByCategorySlug: async (slug: string, page: number = 1, limit: number = 20): Promise<any> => {
    try {
      console.log(`Fetching products for category slug ${slug}, page ${page}...`);
      const response = await fetch(`${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/categories/slug/${slug}/products?page=${page}&limit=${limit}`, {
        next: { revalidate: 86400 } // 24 hours cache
      });
      return await handleResponse<any>(response);
    } catch (error) {
      console.error(`Failed to fetch products for category slug ${slug}:`, error);
      throw error;
    }
  },

  // Get product by slug
  getProductBySlug: async (slug: string, password?: string): Promise<any> => {
    try {
      console.log(`Fetching product by slug: ${slug}`);
      const url = password
        ? `${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/products/slug/${slug}?password=${password}`
        : `${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/products/slug/${slug}`;

      const response = await fetch(url, {
        next: { revalidate: 86400 } // 24 hours cache
      });
      return await handleResponse<any>(response);
    } catch (error) {
      console.error(`Failed to fetch product by slug ${slug}:`, error);
      throw error;
    }
  },

  // Get all products with pagination
  getAllProducts: async (page: number = 1, limit: number = 20): Promise<any> => {
    try {
      console.log(`Fetching all products, page ${page}...`);
      const response = await fetch(`${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/products?page=${page}&limit=${limit}`, {
        next: { revalidate: 86400 } // 24 hours cache
      });
      return await handleResponse<any>(response);
    } catch (error) {
      console.error('Failed to fetch all products:', error);
      throw error;
    }
  },

  // Get all product names for sitemap generation
  getAllProductNames: async (): Promise<any[]> => {
    try {
      console.log('Fetching all product names for sitemap...');
      const response = await fetch(`${SHOP_API_BASE_URL}${SHOP_API_PREFIX}/products/names`, {
        next: { revalidate: 86400 } // 24 hours cache
      });
      return await handleResponse<any[]>(response);
    } catch (error) {
      console.error('Failed to fetch product names:', error);
      throw error;
    }
  },
};