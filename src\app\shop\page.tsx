import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { shopApi } from '@/services/api'

// Revalidate every hour for shop page
export const revalidate = 3600

interface ShopPageProps {
  searchParams: { page?: string }
}

// Generate metadata for SEO
export const metadata: Metadata = {
  title: 'Shop - Premium Organic Beauty Products',
  description: 'Discover our complete collection of premium organic beauty products. High-quality skincare, haircare, and wellness products with natural ingredients.',
  keywords: ['organic beauty', 'skincare', 'natural products', 'premium cosmetics', 'wellness', 'shop'],
  openGraph: {
    title: 'Shop - Premium Organic Beauty Products',
    description: 'Discover our complete collection of premium organic beauty products.',
    type: 'website',
    siteName: 'CocoJojo',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Shop - Premium Organic Beauty Products',
    description: 'Discover our complete collection of premium organic beauty products.',
  },
}

export default async function ShopPage({ searchParams }: ShopPageProps) {
  try {
    const page = parseInt(searchParams.page || '1', 10)
    const limit = 20

    // Get main categories
    const mainCategories = await shopApi.getMainCategories()
    const categories = mainCategories

    // Get featured products for the main shop page
    const productsData = await shopApi.getAllProducts(page, limit)
    const products = productsData.data || []
    const pagination = productsData.pagination || { total: 0, page: 1, limit: 20 }

    // Calculate pagination info
    const totalPages = Math.ceil(pagination.total / pagination.limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    // Enhanced JSON-LD structured data
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'

    // Organization structured data
    const organizationJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'CocoJojo',
      url: baseUrl,
      logo: `${baseUrl}/logo.png`,
      description: 'Premium organic beauty products with natural ingredients',
    }

    // Collection page structured data
    const collectionJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: 'Shop - Premium Organic Beauty Products',
      description: 'Complete collection of premium organic beauty products',
      url: `${baseUrl}/shop`,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: categories.length,
        itemListElement: categories.map((category: any, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Thing',
            name: category.name,
            url: `${baseUrl}/shop/categories/${category.slug}`,
            description: `Browse our ${category.name.toLowerCase()} collection`,
          },
        })),
      },
    }

    return (
      <>
        {/* JSON-LD structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(collectionJsonLd) }}
        />

        <div className="min-h-screen bg-white">
          {/* Hero Section */}
          <div className="relative bg-gradient-to-r from-gray-50 to-gray-100 py-16">
            <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
              <div className="max-w-4xl">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                  Shop Premium Organic Beauty Products
                </h1>
                <p className="text-xl text-gray-600 mb-6">
                  Discover our complete collection of high-quality skincare, haircare, and wellness products with natural ingredients.
                </p>
                <nav className="text-sm text-gray-500">
                  <Link href="/" className="hover:text-gray-700">Home</Link>
                  <span className="mx-2">/</span>
                  <span className="text-gray-900">Shop</span>
                </nav>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
            {/* Categories Grid */}
            <section className="mb-16">
              <h2 className="text-2xl font-semibold text-gray-900 mb-8">Browse by Category</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {categories.map((category: any) => (
                  <Link
                    key={category.id}
                    href={`/shop/categories/${category.slug}`}
                    className="group bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                  >
                    <div className="aspect-square relative overflow-hidden rounded-t-lg">
                      {category.imageUrl ? (
                        <Image
                          src={category.imageUrl}
                          alt={category.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-200"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                          <span className="text-gray-400 text-4xl">📦</span>
                        </div>
                      )}
                    </div>
                    <div className="p-4">
                      <h3 className="font-medium text-gray-900 group-hover:text-main-color transition-colors">
                        {category.name}
                      </h3>
                      <p className="text-sm text-gray-500 mt-1">
                        {category.count || 0} products
                      </p>
                    </div>
                  </Link>
                ))}
              </div>
            </section>

            {/* Featured Products */}
            {products.length > 0 && (
              <section>
                <div className="flex justify-between items-center mb-8">
                  <h2 className="text-2xl font-semibold text-gray-900">Featured Products</h2>
                  {totalPages > 1 && (
                    <div className="text-sm text-gray-500">
                      Page {page} of {totalPages}
                    </div>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
                  {products.map((product: any) => (
                    <Link
                      key={product.id}
                      href={`/shop/${product.slug}`}
                      className="group bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                    >
                      <div className="aspect-square relative overflow-hidden rounded-t-lg">
                        {product.imageUrl ? (
                          <Image
                            src={product.imageUrl}
                            alt={product.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                        ) : (
                          <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                            <span className="text-gray-400 text-4xl">📦</span>
                          </div>
                        )}
                      </div>
                      <div className="p-4">
                        <h3 className="font-medium text-gray-900 group-hover:text-main-color transition-colors line-clamp-2">
                          {product.name}
                        </h3>
                        <div className="flex items-center gap-2 mt-2">
                          {product.salePrice ? (
                            <>
                              <span className="text-lg font-semibold text-main-color">
                                ${product.salePrice.toFixed(2)}
                              </span>
                              <span className="text-sm text-gray-500 line-through">
                                ${product.price.toFixed(2)}
                              </span>
                            </>
                          ) : (
                            <span className="text-lg font-semibold text-gray-900">
                              ${product.price.toFixed(2)}
                            </span>
                          )}
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>

                {/* Pagination */}
                {totalPages > 1 && (
                  <div className="flex justify-center items-center gap-2 my-8">
                    {/* Previous Button */}
                    {hasPrevPage && (
                      <Link
                        href={`/shop?page=${page - 1}`}
                        className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        Previous
                      </Link>
                    )}

                    {/* Page Numbers */}
                    {Array.from({ length: Math.min(totalPages, 10) }).map((_, idx) => {
                      let pageNum: number

                      if (totalPages <= 10) {
                        pageNum = idx + 1
                      } else {
                        // Show pages around current page
                        const start = Math.max(1, page - 4)
                        const end = Math.min(totalPages, start + 9)
                        pageNum = start + idx

                        if (pageNum > end) return null
                      }

                      return (
                        <Link
                          key={pageNum}
                          href={`/shop?page=${pageNum}`}
                          className={`px-3 py-2 rounded-lg transition-colors ${
                            pageNum === page
                              ? 'bg-main-color text-white'
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                          }`}
                        >
                          {pageNum}
                        </Link>
                      )
                    })}

                    {/* Next Button */}
                    {hasNextPage && (
                      <Link
                        href={`/shop?page=${page + 1}`}
                        className="px-4 py-2 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
                      >
                        Next
                      </Link>
                    )}
                  </div>
                )}
              </section>
            )}
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading shop page:', error)
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h1>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      </div>
    )
  }
}
