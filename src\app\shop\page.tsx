"use client";

import { useState, useEffect } from "react";
import { FiFilter, FiX, FiChevronDown, FiGrid, FiList } from "react-icons/fi";
import CategoryFilter from "@/components/shop/CategoryFilter";
import ProductGrid from "@/components/shop/ProductGrid";
import ProductList from "@/components/shop/ProductList";
import FeaturedProducts from "@/components/shop/FeaturedProducts";
import CategoryHierarchy from "@/components/shop/CategoryHierarchy";
import { categories, loadCategoriesWithSubcategories } from "@/data/hierarchicalCategories";

export default function ShopPage() {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<number | null>(null);
  // Price range and sort options for filtering
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [sortBy, setSortBy] = useState<string>("featured");
  const [isLoading, setIsLoading] = useState(true);
  const [categoriesData, setCategoriesData] = useState(categories);

  // Load categories from API when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      try {
        const data = await loadCategoriesWithSubcategories();
        setCategoriesData(data);
      } catch (error) {
        console.error('Error loading categories:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Reset subcategory when category changes
  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    setSelectedSubcategory(null);
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (subcategoryId: number | null) => {
    setSelectedSubcategory(subcategoryId);
  };

  // Handle price range change
  const handlePriceRangeChange = (newRange: [number, number]) => {
    setPriceRange(newRange);
    // In a real implementation, this would filter products by price
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section - Removed */}

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Loading State */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-main-color"></div>
          </div>
        ) : (
          <>
            {/* Category Hierarchy Navigation */}
            <section className="mb-16">
              <CategoryHierarchy
                categories={categoriesData}
                selectedCategory={selectedCategory}
                selectedSubcategory={selectedSubcategory}
                onSelectCategory={handleCategorySelect}
                onSelectSubcategory={handleSubcategorySelect}
              />
            </section>

            {/* Featured Products Section - Only show on main shop page
            {!selectedCategory && !selectedSubcategory && (
              <section className="mb-16">
                <h2 className="text-2xl font-medium text-gray-800 mb-8">Featured Products</h2>
                <FeaturedProducts />
              </section>
            )} */}
          </>
        )}

        {/* Products Section with Filters */}
        <section>
          <div className="flex flex-col md:flex-row gap-8">
            {/* Filters - Mobile Toggle */}
            <div className="md:hidden flex justify-between items-center mb-4">
              <button
                onClick={() => setShowFilters(!showFilters)}
                className="flex items-center gap-2 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg text-gray-700 transition-colors"
              >
                <FiFilter size={18} />
                <span>Filters</span>
                {showFilters ? <FiX size={18} /> : <FiChevronDown size={18} />}
              </button>

              <div className="flex items-center gap-2">
                <button
                  onClick={() => setViewMode("grid")}
                  className={`p-2 rounded-lg ${viewMode === "grid" ? "bg-main-color text-white" : "bg-gray-100 text-gray-700"}`}
                >
                  <FiGrid size={18} />
                </button>
                <button
                  onClick={() => setViewMode("list")}
                  className={`p-2 rounded-lg ${viewMode === "list" ? "bg-main-color text-white" : "bg-gray-100 text-gray-700"}`}
                >
                  <FiList size={18} />
                </button>
              </div>
            </div>

            {/* Filters Sidebar */}
            <div className={`md:w-1/4 ${showFilters ? 'block' : 'hidden md:block'}`}>
              <div className="bg-white rounded-lg shadow-sm border p-6 sticky top-24">
                <div className="hidden md:flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium text-gray-800">Filters</h3>
                  <button className="text-sm text-main-color hover:underline">Reset All</button>
                </div>

                {/* Category Filter */}
                <CategoryFilter
                  categories={categoriesData}
                  selectedCategory={selectedCategory}
                  onSelectCategory={(id) => handleCategorySelect(id === selectedCategory ? null : id)}
                />

                {/* Price Range Filter */}
                <div className="mb-6">
                  <h4 className="text-sm font-medium text-gray-700 mb-4">Price Range</h4>
                  <div className="px-2">
                    <div className="relative h-1 bg-gray-200 rounded-full">
                      <div
                        className="absolute h-1 bg-main-color rounded-full"
                        style={{
                          left: `${priceRange[0]}%`,
                          right: `${100 - priceRange[1]}%`
                        }}
                      ></div>
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={priceRange[0]}
                        onChange={(e) => handlePriceRangeChange([parseInt(e.target.value), priceRange[1]])}
                        className="absolute w-full h-1 opacity-0 cursor-pointer"
                      />
                      <input
                        type="range"
                        min="0"
                        max="100"
                        value={priceRange[1]}
                        onChange={(e) => handlePriceRangeChange([priceRange[0], parseInt(e.target.value)])}
                        className="absolute w-full h-1 opacity-0 cursor-pointer"
                      />
                      <div
                        className="absolute w-4 h-4 bg-white border-2 border-main-color rounded-full -mt-1.5 pointer-events-none"
                        style={{ left: `${priceRange[0]}%` }}
                      ></div>
                      <div
                        className="absolute w-4 h-4 bg-white border-2 border-main-color rounded-full -mt-1.5 pointer-events-none"
                        style={{ left: `${priceRange[1]}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between mt-4">
                      <span className="text-sm text-gray-600">${priceRange[0]}</span>
                      <span className="text-sm text-gray-600">${priceRange[1]}</span>
                    </div>
                  </div>
                </div>

                {/* More filters can be added here */}
                <div className="pt-4 border-t">
                  <button className="w-full bg-main-color text-white py-2 rounded-lg hover:bg-main-color/90 transition-colors">
                    Apply Filters
                  </button>
                </div>
              </div>
            </div>

            {/* Products Grid/List */}
            <div className="md:w-3/4">
              {/* Sort and View Controls - Desktop */}
              <div className="hidden md:flex justify-between items-center mb-6">
                <div className="flex items-center gap-3">
                  <span className="text-sm text-gray-500">Sort by:</span>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="bg-white border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color"
                  >
                    <option value="featured">Featured</option>
                    <option value="newest">Newest</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                  </select>
                </div>

                <div className="flex items-center gap-2">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded-lg ${viewMode === "grid" ? "bg-main-color text-white" : "bg-gray-100 text-gray-700"}`}
                  >
                    <FiGrid size={18} />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 rounded-lg ${viewMode === "list" ? "bg-main-color text-white" : "bg-gray-100 text-gray-700"}`}
                  >
                    <FiList size={18} />
                  </button>
                </div>
              </div>

              {/* Mobile Sort */}
              <div className="md:hidden mb-6">
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="w-full bg-white border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color"
                >
                  <option value="featured">Sort by: Featured</option>
                  <option value="newest">Sort by: Newest</option>
                  <option value="price-low">Sort by: Price: Low to High</option>
                  <option value="price-high">Sort by: Price: High to Low</option>
                </select>
              </div>

              {/* Products Display */}
              {viewMode === "grid" ? (
                <ProductGrid
                  selectedCategory={selectedCategory}
                  selectedSubcategory={selectedSubcategory}
                />
              ) : (
                <ProductList
                  selectedCategory={selectedCategory}
                  selectedSubcategory={selectedSubcategory}
                />
              )}
            </div>
          </div>
        </section>
      </div>
    </div>
  );
}
