import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { shopApi } from '@/services/api'

// Revalidate every hour for shop page
export const revalidate = 3600

interface ShopPageProps {
  searchParams: { page?: string }
}

// Generate metadata for SEO
export const metadata: Metadata = {
  title: 'Shop - Premium Organic Beauty Products',
  description: 'Discover our complete collection of premium organic beauty products. High-quality skincare, haircare, and wellness products with natural ingredients.',
  keywords: ['organic beauty', 'skincare', 'natural products', 'premium cosmetics', 'wellness', 'shop'],
  openGraph: {
    title: 'Shop - Premium Organic Beauty Products',
    description: 'Discover our complete collection of premium organic beauty products.',
    type: 'website',
    siteName: 'CocoJojo',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Shop - Premium Organic Beauty Products',
    description: 'Discover our complete collection of premium organic beauty products.',
  },
}

export default async function ShopPage({ searchParams }: ShopPageProps) {
  try {
    const page = parseInt(searchParams.page || '1', 10)
    const limit = 20

    // Get main categories
    const mainCategories = await shopApi.getMainCategories()
    const categories = mainCategories

    // Get featured products for the main shop page
    const productsData = await shopApi.getAllProducts(page, limit)
    const products = productsData.data || []
    const pagination = productsData.pagination || { total: 0, page: 1, limit: 20 }

    // Calculate pagination info
    const totalPages = Math.ceil(pagination.total / pagination.limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    // Enhanced JSON-LD structured data
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'

    // Organization structured data
    const organizationJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'CocoJojo',
      url: baseUrl,
      logo: `${baseUrl}/logo.png`,
      description: 'Premium organic beauty products with natural ingredients',
    }

    // Collection page structured data
    const collectionJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: 'Shop - Premium Organic Beauty Products',
      description: 'Complete collection of premium organic beauty products',
      url: `${baseUrl}/shop`,
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: categories.length,
        itemListElement: categories.map((category: any, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Thing',
            name: category.name,
            url: `${baseUrl}/shop/categories/${category.slug}`,
            description: `Browse our ${category.name.toLowerCase()} collection`,
          },
        })),
      },
    }

    return (
      <>
        {/* JSON-LD structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(collectionJsonLd) }}
        />

        <div className="min-h-screen bg-white">
          {/* Hero Section */}
          <div className="relative bg-gradient-to-br from-main-color/10 via-white to-main-color/5 py-20">
            <div className="absolute inset-0 bg-[url('/hero-pattern.svg')] opacity-5"></div>
            <div className="relative px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
              <div className="max-w-4xl">
                <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6 leading-tight">
                  Shop Premium
                  <span className="text-main-color block">Organic Beauty</span>
                </h1>
                <p className="text-xl text-gray-600 mb-8 leading-relaxed max-w-2xl">
                  Discover our complete collection of high-quality skincare, haircare, and wellness products crafted with natural ingredients for your beauty journey.
                </p>
                <nav className="text-sm text-gray-500 mb-8">
                  <Link href="/" className="hover:text-main-color transition-colors">Home</Link>
                  <span className="mx-2">/</span>
                  <span className="text-gray-900 font-medium">Shop</span>
                </nav>
                <div className="flex flex-wrap gap-4">
                  <div className="bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200">
                    <span className="text-sm font-medium text-gray-700">✨ Natural Ingredients</span>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200">
                    <span className="text-sm font-medium text-gray-700">🌿 Organic Certified</span>
                  </div>
                  <div className="bg-white/80 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200">
                    <span className="text-sm font-medium text-gray-700">💚 Cruelty Free</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
            {/* Categories Grid */}
            <section className="mb-20">
              <div className="text-center mb-12">
                <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Browse by Category</h2>
                <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                  Explore our carefully curated collections designed to meet all your beauty and wellness needs
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                {categories.map((category: any) => (
                  <Link
                    key={category.id}
                    href={`/shop/categories/${category.slug}`}
                    className="group relative bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
                  >
                    <div className="aspect-[4/3] relative overflow-hidden">
                      {category.imageUrl ? (
                        <Image
                          src={category.imageUrl}
                          alt={`${category.name} - Premium Beauty Products`}
                          fill
                          className="object-cover group-hover:scale-110 transition-transform duration-500"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-main-color/20 to-main-color/5 flex items-center justify-center">
                          <div className="text-center">
                            <div className="text-6xl mb-2 opacity-60">🌿</div>
                            <span className="text-main-color font-medium">Coming Soon</span>
                          </div>
                        </div>
                      )}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 group-hover:text-main-color transition-colors mb-2">
                        {category.name}
                      </h3>
                      <p className="text-gray-600 mb-3">
                        {category.count || 0} premium products
                      </p>
                      <div className="flex items-center text-main-color font-medium text-sm group-hover:translate-x-1 transition-transform duration-200">
                        <span>Explore Collection</span>
                        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
            </section>

            {/* Featured Products */}
            {products.length > 0 && (
              <section className="bg-gray-50 -mx-4 md:-mx-8 lg:-mx-16 xl:-mx-32 2xl:-mx-64 px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Products</h2>
                  <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                    Discover our most popular and highly-rated beauty essentials
                  </p>
                  {totalPages > 1 && (
                    <div className="text-sm text-gray-500 mt-4">
                      Page {page} of {totalPages} • {pagination.total} products total
                    </div>
                  )}
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12">
                  {products.map((product: any) => (
                    <Link
                      key={product.id}
                      href={`/shop/${product.slug}`}
                      className="group bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100"
                    >
                      <div className="aspect-square relative overflow-hidden">
                        {product.imageUrl ? (
                          <Image
                            src={product.imageUrl}
                            alt={`${product.name} - Premium Beauty Product`}
                            fill
                            className="object-cover group-hover:scale-110 transition-transform duration-500"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-main-color/20 to-main-color/5 flex items-center justify-center">
                            <div className="text-center">
                              <div className="text-6xl mb-2 opacity-60">✨</div>
                              <span className="text-main-color font-medium">New Product</span>
                            </div>
                          </div>
                        )}
                        {product.salePrice && (
                          <div className="absolute top-4 left-4 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                            SALE
                          </div>
                        )}
                        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>
                      <div className="p-6">
                        <h3 className="font-semibold text-gray-900 group-hover:text-main-color transition-colors line-clamp-2 mb-2">
                          {product.name}
                        </h3>
                        <div className="flex items-center gap-2 mb-3">
                          {product.salePrice ? (
                            <>
                              <span className="text-xl font-bold text-main-color">
                                ${product.salePrice.toFixed(2)}
                              </span>
                              <span className="text-sm text-gray-500 line-through">
                                ${product.price.toFixed(2)}
                              </span>
                              <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded-full font-medium">
                                {Math.round(((product.price - product.salePrice) / product.price) * 100)}% OFF
                              </span>
                            </>
                          ) : (
                            <span className="text-xl font-bold text-gray-900">
                              ${product.price.toFixed(2)}
                            </span>
                          )}
                        </div>
                        <div className="flex items-center justify-between">
                          <div className="flex items-center text-yellow-400">
                            {Array.from({ length: 5 }).map((_, i) => (
                              <svg key={i} className="w-4 h-4 fill-current" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                            ))}
                            <span className="text-gray-600 text-sm ml-2">(4.5)</span>
                          </div>
                          <span className={`text-xs px-2 py-1 rounded-full font-medium ${
                            product.stockStatus === 'IN_STOCK'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {product.stockStatus === 'IN_STOCK' ? 'In Stock' : 'Out of Stock'}
                          </span>
                        </div>
                      </div>
                    </Link>
                  ))}
                </div>

                {/* Enhanced Pagination */}
                {totalPages > 1 && (
                  <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
                    <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
                      <div className="text-sm text-gray-600">
                        Showing {((page - 1) * pagination.limit) + 1} to {Math.min(page * pagination.limit, pagination.total)} of {pagination.total} products
                      </div>
                      <div className="flex items-center gap-2">
                        {/* Previous Button */}
                        {hasPrevPage ? (
                          <Link
                            href={`/shop?page=${page - 1}`}
                            className="flex items-center px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                          >
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                            Previous
                          </Link>
                        ) : (
                          <div className="flex items-center px-4 py-2 bg-gray-100 text-gray-400 rounded-lg cursor-not-allowed">
                            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                            </svg>
                            Previous
                          </div>
                        )}

                        {/* Page Numbers */}
                        <div className="flex items-center gap-1">
                          {Array.from({ length: Math.min(totalPages, 7) }).map((_, idx) => {
                            let pageNum: number

                            if (totalPages <= 7) {
                              pageNum = idx + 1
                            } else {
                              // Show pages around current page
                              const start = Math.max(1, page - 3)
                              const end = Math.min(totalPages, start + 6)
                              pageNum = start + idx

                              if (pageNum > end) return null
                            }

                            return (
                              <Link
                                key={pageNum}
                                href={`/shop?page=${pageNum}`}
                                className={`w-10 h-10 flex items-center justify-center rounded-lg transition-colors ${
                                  pageNum === page
                                    ? 'bg-main-color text-white shadow-lg'
                                    : 'bg-white border border-gray-300 text-gray-700 hover:bg-gray-50'
                                }`}
                              >
                                {pageNum}
                              </Link>
                            )
                          })}
                        </div>

                        {/* Next Button */}
                        {hasNextPage ? (
                          <Link
                            href={`/shop?page=${page + 1}`}
                            className="flex items-center px-4 py-2 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
                          >
                            Next
                            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </Link>
                        ) : (
                          <div className="flex items-center px-4 py-2 bg-gray-100 text-gray-400 rounded-lg cursor-not-allowed">
                            Next
                            <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                            </svg>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                )}
              </section>
            )}
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading shop page:', error)
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Something went wrong</h1>
          <p className="text-gray-600">Please try again later.</p>
        </div>
      </div>
    )
  }
}
