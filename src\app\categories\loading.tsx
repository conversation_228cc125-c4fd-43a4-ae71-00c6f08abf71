export default function CategoriesLoading() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section Skeleton */}
      <div className="relative bg-gradient-to-r from-gray-50 to-gray-100 py-16">
        <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
          <div className="max-w-4xl">
            <div className="h-12 bg-gray-300 rounded-lg mb-4 animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded-lg mb-6 w-3/4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded-lg w-1/2 animate-pulse"></div>
          </div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Subcategories Section */}
        <section className="mb-16">
          <div className="h-8 bg-gray-300 rounded-lg w-64 mb-8 animate-pulse"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border">
                <div className="aspect-square bg-gray-300 rounded-t-lg animate-pulse"></div>
                <div className="p-4">
                  <div className="h-5 bg-gray-300 rounded mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-300 rounded w-1/2 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Featured Products Section */}
        <section>
          <div className="flex justify-between items-center mb-8">
            <div className="h-8 bg-gray-300 rounded-lg w-48 animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded-lg w-32 animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm border">
                <div className="aspect-square bg-gray-300 rounded-t-lg animate-pulse"></div>
                <div className="p-4">
                  <div className="h-5 bg-gray-300 rounded mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-300 rounded mb-2 w-3/4 animate-pulse"></div>
                  <div className="h-6 bg-gray-300 rounded w-1/2 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </section>
      </div>
    </div>
  )
}
