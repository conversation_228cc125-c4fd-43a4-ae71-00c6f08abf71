import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { shopApi } from '@/services/api'

// Revalidate every hour for subcategory pages
export const revalidate = 3600

// Control dynamic params - only build known routes at build time
export const dynamicParams = false

interface SubcategoryPageProps {
  params: { mainSlug: string; catSlug: string }
  searchParams: { page?: string }
}

// Generate static params for all subcategories
export async function generateStaticParams({ params }: { params: { mainSlug: string } }) {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300'
    
    // First, fetch all main categories to find the current one
    const mainResponse = await fetch(`${baseUrl}/api/shop/main-categories`, {
      cache: 'no-store'
    })
    
    if (!mainResponse.ok) {
      console.error('Failed to fetch main categories for subcategory static params')
      return []
    }
    
    const mainData = await mainResponse.json()
    const categories = mainData.data || mainData
    const mainCategory = categories.find((cat: any) => cat.slug === params.mainSlug)
    
    if (!mainCategory) {
      console.error(`Main category not found for slug: ${params.mainSlug}`)
      return []
    }

    // Fetch subcategories for this specific main category
    const subResponse = await fetch(`${baseUrl}/api/shop/main-categories/${mainCategory.id}/categories`, {
      cache: 'no-store'
    })
    
    if (!subResponse.ok) {
      console.error(`Failed to fetch subcategories for main category: ${mainCategory.id}`)
      return []
    }
    
    const subData = await subResponse.json()
    const subcategories = subData.subcategories || []
    
    return subcategories.map((subcategory: any) => ({
      catSlug: subcategory.slug,
    }))
  } catch (error) {
    console.error('Error generating static params for subcategories:', error)
    return []
  }
}

// Generate metadata for SEO with pagination support
export async function generateMetadata({ params, searchParams }: SubcategoryPageProps): Promise<Metadata> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const page = parseInt(searchParams.page || '1', 10)
    
    const mainCategories = await shopApi.getMainCategories()
    const categories = mainCategories.data || mainCategories
    const mainCategory = categories.find((cat: any) => cat.slug === params.mainSlug)

    if (!mainCategory) {
      return {
        title: 'Category Not Found',
        description: 'The requested category could not be found.',
      }
    }

    const subcategoriesData = await shopApi.getSubcategories(mainCategory.id)
    const subcategories = subcategoriesData.subcategories || []
    const subcategory = subcategories.find((subcat: any) => subcat.slug === params.catSlug)

    if (!subcategory) {
      return {
        title: 'Subcategory Not Found',
        description: 'The requested subcategory could not be found.',
      }
    }

    // Get pagination info for rel prev/next
    const productsData = await shopApi.getProductsByCategorySlug(params.catSlug, page, 20)
    const pagination = productsData.pagination || { total: 0, page: 1, limit: 20 }
    const totalPages = Math.ceil(pagination.total / pagination.limit)
    
    const currentUrl = `${baseUrl}/shop/categories/${mainCategory.slug}/${subcategory.slug}`
    const pageTitle = page > 1 ? `${subcategory.name} - Page ${page} - ${mainCategory.name}` : `${subcategory.name} - ${mainCategory.name}`
    const pageDescription = page > 1 
      ? `Shop ${subcategory.name} in our ${mainCategory.name.toLowerCase()} collection - Page ${page} of ${totalPages}. Premium quality products.`
      : `Shop ${subcategory.name} in our ${mainCategory.name.toLowerCase()} collection. Premium quality products with ${subcategory.count || 0} items available.`

    // Pagination URLs
    const prevPage = page > 1 ? `${currentUrl}?page=${page - 1}` : null
    const nextPage = page < totalPages ? `${currentUrl}?page=${page + 1}` : null
    const canonicalUrl = page > 1 ? `${currentUrl}?page=${page}` : currentUrl

    return {
      title: pageTitle,
      description: pageDescription,
      keywords: [subcategory.name, mainCategory.name, 'products', 'shop', 'buy online'],
      robots: page > 1 ? 'noindex, follow' : 'index, follow', // Avoid indexing pagination pages
      openGraph: {
        title: `${subcategory.name} - ${mainCategory.name}`,
        description: pageDescription,
        url: canonicalUrl,
        images: subcategory.imageUrl ? [{ url: subcategory.imageUrl, alt: subcategory.name }] : [],
        type: 'website',
      },
      twitter: {
        card: 'summary_large_image',
        title: `${subcategory.name} - ${mainCategory.name}`,
        description: pageDescription,
        images: subcategory.imageUrl ? [subcategory.imageUrl] : [],
      },
      alternates: {
        canonical: canonicalUrl,
      },
      other: {
        ...(prevPage ? { 'link-rel-prev': `<link rel="prev" href="${prevPage}" />` } : {}),
        ...(nextPage ? { 'link-rel-next': `<link rel="next" href="${nextPage}" />` } : {}),
      },
    }
  } catch (error) {
    console.error('Error generating metadata for subcategory:', error)
    return {
      title: 'Products',
      description: 'Browse our product collection.',
    }
  }
}

export default async function SubcategoryPage({ params, searchParams }: SubcategoryPageProps) {
  try {
    const page = parseInt(searchParams.page || '1', 10)
    const limit = 20

    // Get main categories to find the current one
    const mainCategories = await shopApi.getMainCategories()
    const categories = mainCategories.data || mainCategories
    const mainCategory = categories.find((cat: any) => cat.slug === params.mainSlug)

    if (!mainCategory) {
      notFound()
    }

    // Get subcategories for this main category
    const subcategoriesData = await shopApi.getSubcategories(mainCategory.id)
    const subcategories = subcategoriesData.subcategories || []
    const currentSubcategory = subcategories.find((subcat: any) => subcat.slug === params.catSlug)

    if (!currentSubcategory) {
      notFound()
    }

    // Get products for this subcategory
    const productsData = await shopApi.getProductsByCategorySlug(params.catSlug, page, limit)
    const products = productsData.data || []
    const pagination = productsData.pagination || { total: 0, page: 1, limit: 20 }

    // Calculate pagination info
    const totalPages = Math.ceil(pagination.total / pagination.limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    // JSON-LD structured data
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const jsonLd = {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: `${currentSubcategory.name} - ${mainCategory.name}`,
      description: `Shop ${currentSubcategory.name} in our ${mainCategory.name.toLowerCase()} collection`,
      url: `${baseUrl}/shop/categories/${mainCategory.slug}/${currentSubcategory.slug}`,
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            item: { '@id': '/', name: 'Home' },
          },
          {
            '@type': 'ListItem',
            position: 2,
            item: { '@id': '/shop', name: 'Shop' },
          },
          {
            '@type': 'ListItem',
            position: 3,
            item: { '@id': `/shop/categories/${mainCategory.slug}`, name: mainCategory.name },
          },
          {
            '@type': 'ListItem',
            position: 4,
            item: { '@id': `/shop/categories/${mainCategory.slug}/${currentSubcategory.slug}`, name: currentSubcategory.name },
          },
        ],
      },
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: pagination.total,
        itemListElement: products.map((product: any, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Product',
            name: product.name,
            url: `/shop/${product.slug}`,
            image: product.imageUrl,
            offers: {
              '@type': 'Offer',
              price: product.salePrice || product.price,
              priceCurrency: 'USD',
              availability: product.inStock ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
            },
          },
        })),
      },
    }

    return (
      <>
        {/* JSON-LD structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />

        <div className="min-h-screen bg-white">
          {/* Hero Section */}
          <div className="relative bg-gradient-to-r from-gray-50 to-gray-100 py-16">
            <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
              <div className="max-w-4xl">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                  {currentSubcategory.name}
                </h1>
                <p className="text-xl text-gray-600 mb-6">
                  Discover our premium {currentSubcategory.name.toLowerCase()} collection with {currentSubcategory.count || 0} products
                </p>
                <nav className="text-sm text-gray-500">
                  <Link href="/" className="hover:text-gray-700">Home</Link>
                  <span className="mx-2">/</span>
                  <Link href="/shop" className="hover:text-gray-700">Shop</Link>
                  <span className="mx-2">/</span>
                  <Link href={`/shop/categories/${params.mainSlug}`} className="hover:text-gray-700">
                    {mainCategory.name}
                  </Link>
                  <span className="mx-2">/</span>
                  <span className="text-gray-900">{currentSubcategory.name}</span>
                </nav>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
            {/* Products Grid */}
            <section>
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-900">
                  Products ({pagination.total})
                </h2>
                <div className="text-sm text-gray-500">
                  Page {page} of {totalPages}
                </div>
              </div>

              {products.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
                    {products.map((product: any) => (
                      <Link
                        key={product.id}
                        href={`/shop/${product.slug}`}
                        className="group bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                      >
                        <div className="aspect-square relative overflow-hidden rounded-t-lg">
                          {product.imageUrl ? (
                            <Image
                              src={product.imageUrl}
                              alt={product.name}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-200"
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                              <span className="text-gray-400 text-4xl">📦</span>
                            </div>
                          )}
                        </div>
                        <div className="p-4">
                          <h3 className="font-medium text-gray-900 group-hover:text-main-color transition-colors line-clamp-2">
                            {product.name}
                          </h3>
                          <div className="flex items-center gap-2 mt-2">
                            {product.salePrice ? (
                              <>
                                <span className="text-lg font-semibold text-main-color">
                                  ${product.salePrice.toFixed(2)}
                                </span>
                                <span className="text-sm text-gray-500 line-through">
                                  ${product.price.toFixed(2)}
                                </span>
                              </>
                            ) : (
                              <span className="text-lg font-semibold text-gray-900">
                                ${product.price.toFixed(2)}
                              </span>
                            )}
                          </div>
                          <div className="mt-2">
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              product.inStock 
                                ? 'bg-green-100 text-green-800' 
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {product.inStock ? 'In Stock' : 'Out of Stock'}
                            </span>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>

                  {/* Enhanced Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center items-center gap-2 my-8">
                      {/* Previous Button */}
                      {hasPrevPage && (
                        <Link
                          href={`/shop/categories/${params.mainSlug}/${params.catSlug}?page=${page - 1}`}
                          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                        >
                          Previous
                        </Link>
                      )}
                      
                      {/* Page Numbers */}
                      {Array.from({ length: Math.min(totalPages, 10) }).map((_, idx) => {
                        let pageNum: number
                        
                        if (totalPages <= 10) {
                          pageNum = idx + 1
                        } else {
                          // Show pages around current page
                          const start = Math.max(1, page - 4)
                          const end = Math.min(totalPages, start + 9)
                          pageNum = start + idx
                          
                          if (pageNum > end) return null
                        }
                        
                        return (
                          <Link
                            key={pageNum}
                            href={`/shop/categories/${params.mainSlug}/${params.catSlug}?page=${pageNum}`}
                            className={`px-3 py-2 rounded-lg transition-colors ${
                              pageNum === page
                                ? 'bg-main-color text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {pageNum}
                          </Link>
                        )
                      })}
                      
                      {/* Next Button */}
                      {hasNextPage && (
                        <Link
                          href={`/shop/categories/${params.mainSlug}/${params.catSlug}?page=${page + 1}`}
                          className="px-4 py-2 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
                        >
                          Next
                        </Link>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-6xl mb-4">📦</div>
                  <h3 className="text-xl font-medium text-gray-900 mb-2">No products found</h3>
                  <p className="text-gray-600">
                    There are currently no products in this category.
                  </p>
                </div>
              )}
            </section>
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading subcategory page:', error)
    notFound()
  }
}
