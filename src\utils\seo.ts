// SEO utility functions for generating structured data and metadata

export interface Product {
  id: number
  name: string
  slug: string
  description: string
  shortDescription: string
  price: number
  salePrice?: number
  sku: string
  stockStatus: string
  images?: Array<{ url: string; position: number }>
  categories?: Array<{ id: number; name: string; slug: string }>
  tags?: Array<{ id: number; name: string; slug: string }>
  updatedAt?: string
}

export interface Category {
  id: number
  name: string
  slug: string
  count?: number
  imageUrl?: string
}

// Generate product JSON-LD structured data
export function generateProductJsonLd(product: Product, baseUrl: string = '') {
  const price = product.salePrice || product.price
  
  return {
    '@context': 'https://schema.org',
    '@type': 'Product',
    name: product.name,
    description: product.description,
    image: product.images?.map(img => `${baseUrl}${img.url}`) || [],
    sku: product.sku,
    brand: {
      '@type': 'Brand',
      name: 'Your Store',
    },
    offers: {
      '@type': 'Offer',
      price: price.toString(),
      priceCurrency: 'USD',
      availability: product.stockStatus === 'IN_STOCK' 
        ? 'https://schema.org/InStock' 
        : 'https://schema.org/OutOfStock',
      seller: {
        '@type': 'Organization',
        name: 'Your Store',
      },
      url: `${baseUrl}/products/${product.slug}`,
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.5',
      reviewCount: '10',
    },
  }
}

// Generate category JSON-LD structured data
export function generateCategoryJsonLd(category: Category, baseUrl: string = '') {
  return {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: category.name,
    description: `Browse our ${category.name.toLowerCase()} collection`,
    url: `${baseUrl}/categories/${category.slug}`,
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: category.count || 0,
    },
  }
}

// Generate breadcrumb JSON-LD structured data
export function generateBreadcrumbJsonLd(breadcrumbs: Array<{ name: string; href: string }>, baseUrl: string = '') {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      item: {
        '@id': `${baseUrl}${crumb.href}`,
        name: crumb.name,
      },
    })),
  }
}

// Generate organization JSON-LD structured data
export function generateOrganizationJsonLd(baseUrl: string = '') {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'Your Store',
    url: baseUrl,
    logo: `${baseUrl}/logo.png`,
    sameAs: [
      'https://www.facebook.com/yourstore',
      'https://www.twitter.com/yourstore',
      'https://www.instagram.com/yourstore',
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '******-123-4567',
      contactType: 'customer service',
    },
  }
}

// Generate website JSON-LD structured data
export function generateWebsiteJsonLd(baseUrl: string = '') {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'Your Store',
    url: baseUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${baseUrl}/search?q={search_term_string}`,
      'query-input': 'required name=search_term_string',
    },
  }
}

// Generate meta description from product data
export function generateProductMetaDescription(product: Product): string {
  const price = product.salePrice || product.price
  const isOnSale = !!product.salePrice
  const tagNames = product.tags?.map(tag => tag.name).join(', ') || ''
  
  let description = product.shortDescription
  
  if (tagNames) {
    description += ` | Tags: ${tagNames}`
  }
  
  description += ` | Price: $${price.toFixed(2)}`
  
  if (isOnSale && product.price) {
    description += ` (was $${product.price.toFixed(2)})`
  }
  
  // Keep under 160 characters for SEO
  return description.slice(0, 160)
}

// Generate meta keywords from product data
export function generateProductKeywords(product: Product): string[] {
  const keywords = [product.name]
  
  if (product.tags) {
    keywords.push(...product.tags.map(tag => tag.name))
  }
  
  if (product.categories) {
    keywords.push(...product.categories.map(cat => cat.name))
  }
  
  keywords.push('buy online', 'premium quality')
  
  return keywords
}

// Generate canonical URL
export function generateCanonicalUrl(path: string, baseUrl: string = ''): string {
  return `${baseUrl}${path}`
}

// Generate Open Graph image URL
export function generateOgImage(product?: Product, category?: Category, baseUrl: string = ''): string {
  if (product && product.images && product.images.length > 0) {
    return `${baseUrl}${product.images[0].url}`
  }
  
  if (category && category.imageUrl) {
    return `${baseUrl}${category.imageUrl}`
  }
  
  return `${baseUrl}/og-default.jpg`
}
