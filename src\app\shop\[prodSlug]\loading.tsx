export default function ProductLoading() {
  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb Skeleton */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-4 border-b">
        <div className="h-4 bg-gray-300 rounded w-64 animate-pulse"></div>
      </div>

      {/* Product Content Skeleton */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        <div className="flex flex-col lg:flex-row gap-12">
          {/* Product Images Skeleton */}
          <div className="w-full lg:w-1/2">
            <div className="aspect-square bg-gray-300 rounded-lg animate-pulse mb-4"></div>
            <div className="flex gap-2">
              {Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="w-20 h-20 bg-gray-300 rounded animate-pulse"></div>
              ))}
            </div>
          </div>

          {/* Product Details Skeleton */}
          <div className="w-full lg:w-1/2">
            {/* Product Header */}
            <div className="mb-8">
              <div className="h-10 bg-gray-300 rounded-lg mb-4 animate-pulse"></div>
              
              {/* Price */}
              <div className="flex items-center gap-4 mb-4">
                <div className="h-8 bg-gray-300 rounded w-24 animate-pulse"></div>
                <div className="h-10 bg-gray-300 rounded w-32 animate-pulse"></div>
                <div className="h-6 bg-gray-300 rounded w-16 animate-pulse"></div>
              </div>

              {/* SKU and Stock */}
              <div className="flex items-center gap-4 mb-6">
                <div className="h-4 bg-gray-300 rounded w-24 animate-pulse"></div>
                <div className="h-6 bg-gray-300 rounded w-20 animate-pulse"></div>
              </div>
            </div>

            {/* Short Description */}
            <div className="mb-8">
              <div className="h-4 bg-gray-300 rounded mb-2 animate-pulse"></div>
              <div className="h-4 bg-gray-300 rounded mb-2 w-5/6 animate-pulse"></div>
              <div className="h-4 bg-gray-300 rounded w-3/4 animate-pulse"></div>
            </div>

            {/* Categories and Tags */}
            <div className="space-y-4 mb-8">
              <div>
                <div className="h-5 bg-gray-300 rounded w-24 mb-2 animate-pulse"></div>
                <div className="flex flex-wrap gap-2">
                  {Array.from({ length: 3 }).map((_, index) => (
                    <div key={index} className="h-8 bg-gray-300 rounded-full w-20 animate-pulse"></div>
                  ))}
                </div>
              </div>
              <div>
                <div className="h-5 bg-gray-300 rounded w-16 mb-2 animate-pulse"></div>
                <div className="flex flex-wrap gap-2">
                  {Array.from({ length: 4 }).map((_, index) => (
                    <div key={index} className="h-8 bg-gray-300 rounded-full w-16 animate-pulse"></div>
                  ))}
                </div>
              </div>
            </div>

            {/* Add to Cart Button */}
            <div className="h-12 bg-gray-300 rounded-lg w-full animate-pulse"></div>
          </div>
        </div>

        {/* Product Description Skeleton */}
        <div className="mt-16 border-t pt-16">
          <div className="h-8 bg-gray-300 rounded-lg w-48 mb-6 animate-pulse"></div>
          <div className="space-y-3">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={index} className="h-4 bg-gray-300 rounded animate-pulse"></div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
