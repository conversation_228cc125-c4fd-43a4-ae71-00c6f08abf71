import { MetadataRoute } from 'next'
import { shopApi } from '@/services/api'

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'

  try {
    console.log('🗺️  Generating sitemap...')

    // Static pages with optimized priorities
    const staticPages: MetadataRoute.Sitemap = [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1.0,
      },
      {
        url: `${baseUrl}/products`,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 0.9,
      },
      {
        url: `${baseUrl}/about`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.7,
      },
      {
        url: `${baseUrl}/contact`,
        lastModified: new Date(),
        changeFrequency: 'monthly',
        priority: 0.7,
      },
      {
        url: `${baseUrl}/blog`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      },
    ]

    // Get main categories
    console.log('📁 Fetching main categories...')
    const mainCategories = await shopApi.getMainCategories()
    const categoryPages: MetadataRoute.Sitemap = []
    const mainCats = mainCategories.data || mainCategories

    for (const mainCategory of mainCats) {
      // Main category page
      categoryPages.push({
        url: `${baseUrl}/categories/${mainCategory.slug}`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8,
      })

      // Get subcategories for this main category
      try {
        const subcategoriesData = await shopApi.getSubcategories(mainCategory.id)
        const subcategories = subcategoriesData.subcategories || []

        for (const subcategory of subcategories) {
          categoryPages.push({
            url: `${baseUrl}/categories/${mainCategory.slug}/${subcategory.slug}`,
            lastModified: new Date(),
            changeFrequency: 'weekly',
            priority: 0.7,
          })
        }
      } catch (error) {
        console.error(`❌ Error fetching subcategories for ${mainCategory.slug}:`, error)
      }
    }

    // Get all products
    console.log('📦 Fetching all products...')
    const products = await shopApi.getAllProductNames()
    const productList = products.data || products
    const productPages: MetadataRoute.Sitemap = productList.map((product: any) => ({
      url: `${baseUrl}/products/${product.slug}`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9, // Higher priority for products as they are most critical for sales
    }))

    // Log statistics
    console.log('📊 Sitemap Statistics:')
    console.log(`   📄 Static pages: ${staticPages.length}`)
    console.log(`   📁 Category pages: ${categoryPages.length}`)
    console.log(`   📦 Product pages: ${productPages.length}`)
    console.log(`   🔗 Total URLs: ${staticPages.length + categoryPages.length + productPages.length}`)

    return [...staticPages, ...categoryPages, ...productPages]
  } catch (error) {
    console.error('Error generating sitemap:', error)

    // Return minimal sitemap if API fails
    return [
      {
        url: baseUrl,
        lastModified: new Date(),
        changeFrequency: 'daily',
        priority: 1,
      },
    ]
  }
}
