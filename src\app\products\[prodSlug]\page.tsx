import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { shopApi } from '@/services/api'
import ProductImageGallery from '@/components/shop/product/ProductImageGallery'
import ProductVariantSelector from '@/components/shop/product/ProductVariantSelector'
import ProductListings from '@/components/shop/product/ProductListings'

// Revalidate every 24 hours for product pages (prices and inventory can change daily)
export const revalidate = 86400

interface ProductPageProps {
  params: { prodSlug: string }
}

// Control dynamic params - only build known routes at build time
export const dynamicParams = false

// Generate static params for all products
export async function generateStaticParams() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300'
    const response = await fetch(`${baseUrl}/api/shop/products/names`, {
      // No cache during build time to get fresh data
      cache: 'no-store'
    })

    if (!response.ok) {
      console.error('Failed to fetch product names for static params')
      return []
    }

    const data = await response.json()
    const products = data.data || data

    return products.map((product: any) => ({
      prodSlug: product.slug,
    }))
  } catch (error) {
    console.error('Error generating static params for products:', error)
    return []
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'
    const product = await shopApi.getProductBySlug(params.prodSlug)

    if (!product) {
      return {
        title: 'Product Not Found',
        description: 'The requested product could not be found.',
      }
    }

    const price = product.salePrice || product.price
    const originalPrice = product.salePrice ? product.price : null
    const isOnSale = !!product.salePrice

    // Create rich description using shortDescription and tags
    const tagNames = product.tags?.map((tag: any) => tag.name).join(', ') || ''
    const categoryNames = product.categories?.map((cat: any) => cat.name).join(' > ') || ''

    let description = product.shortDescription
    if (tagNames) description += ` | Tags: ${tagNames}`
    if (categoryNames) description += ` | Category: ${categoryNames}`
    description += ` | Price: $${price.toFixed(2)}`
    if (isOnSale && originalPrice) description += ` (was $${originalPrice.toFixed(2)})`

    const title = `${product.name}${categoryNames ? ` | ${categoryNames}` : ''} - Your Store`
    const url = `${baseUrl}/products/${product.slug}`

    return {
      title,
      description: description.slice(0, 160), // Keep under 160 chars for SEO
      keywords: [
        product.name,
        ...(product.tags?.map((tag: any) => tag.name) || []),
        ...(product.categories?.map((cat: any) => cat.name) || []),
        'buy online',
        'premium quality'
      ],
      openGraph: {
        title: product.name,
        description: product.shortDescription,
        url,
        siteName: 'Your Store',
        images: product.images?.length > 0 ? [
          {
            url: product.images[0].url,
            alt: product.name,
            width: 800,
            height: 800,
          }
        ] : [],
        type: 'product',
        locale: 'en_US',
      },
      twitter: {
        card: 'summary_large_image',
        title: product.name,
        description: product.shortDescription,
        images: product.images?.length > 0 ? [product.images[0].url] : [],
      },
      alternates: {
        canonical: url,
      },
    }
  } catch (error) {
    console.error('Error generating metadata for product:', error)
    return {
      title: 'Product',
      description: 'View product details.',
    }
  }
}

export default async function ProductPage({ params }: ProductPageProps) {
  try {
    const product = await shopApi.getProductBySlug(params.prodSlug)

    if (!product) {
      notFound()
    }

    const price = product.salePrice || product.price
    const originalPrice = product.salePrice ? product.price : null
    const isOnSale = !!product.salePrice

    // Create breadcrumb from categories
    const mainCategory = product.categories?.[0]
    const breadcrumbs = [
      { name: 'Home', href: '/' },
      { name: 'Products', href: '/products' },
    ]

    if (mainCategory) {
      breadcrumbs.push({
        name: mainCategory.name,
        href: `/categories/${mainCategory.slug}`,
      })
    }

    breadcrumbs.push({ name: product.name, href: `/products/${product.slug}` })

    // Enhanced JSON-LD structured data
    const baseUrl = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'

    // Product structured data
    const productJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Product',
      name: product.name,
      description: product.description,
      image: product.images?.map((img: any) => `${baseUrl}${img.url}`) || [],
      sku: product.sku,
      brand: {
        '@type': 'Brand',
        name: 'Your Store',
      },
      category: product.categories?.map((cat: any) => cat.name).join(', ') || '',
      offers: {
        '@type': 'Offer',
        url: `${baseUrl}/products/${product.slug}`,
        price: price.toString(),
        priceCurrency: 'USD',
        availability: product.stockStatus === 'IN_STOCK'
          ? 'https://schema.org/InStock'
          : 'https://schema.org/OutOfStock',
        priceValidUntil: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 30 days from now
        seller: {
          '@type': 'Organization',
          name: 'Your Store',
        },
      },
      aggregateRating: {
        '@type': 'AggregateRating',
        ratingValue: '4.5',
        reviewCount: '10',
      },
    }

    // Breadcrumb structured data
    const breadcrumbJsonLd = {
      '@type': 'BreadcrumbList',
      '@context': 'https://schema.org',
      itemListElement: breadcrumbs.map((crumb, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@id': `${baseUrl}${crumb.href}`,
          name: crumb.name,
        },
      })),
    }

    // Organization structured data
    const organizationJsonLd = {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: 'Your Store',
      url: baseUrl,
      logo: `${baseUrl}/logo.png`,
    }

    return (
      <>
        {/* JSON-LD structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(productJsonLd) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(breadcrumbJsonLd) }}
        />
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationJsonLd) }}
        />

        <div className="min-h-screen bg-white">
          {/* Breadcrumb Navigation */}
          <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-4 border-b">
            <nav className="text-sm text-gray-500">
              {breadcrumbs.map((crumb, index) => (
                <span key={index}>
                  {index < breadcrumbs.length - 1 ? (
                    <Link href={crumb.href} className="hover:text-gray-700">
                      {crumb.name}
                    </Link>
                  ) : (
                    <span className="text-gray-900">{crumb.name}</span>
                  )}
                  {index < breadcrumbs.length - 1 && <span className="mx-2">/</span>}
                </span>
              ))}
            </nav>
          </div>

          {/* Product Content */}
          <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
            <div className="flex flex-col lg:flex-row gap-12">
              {/* Product Images */}
              <div className="w-full lg:w-1/2">
                <ProductImageGallery images={product.images || []} />
              </div>

              {/* Product Details */}
              <div className="w-full lg:w-1/2">
                {/* Product Header */}
                <div className="mb-8">
                  <h1 className="text-4xl font-bold text-gray-900 mb-4">{product.name}</h1>

                  {/* Price */}
                  <div className="flex items-center gap-4 mb-4">
                    {originalPrice && (
                      <span className="text-xl text-gray-500 line-through">
                        ${originalPrice.toFixed(2)}
                      </span>
                    )}
                    <span className="text-3xl font-bold text-main-color">
                      ${price.toFixed(2)}
                    </span>
                    {isOnSale && (
                      <span className="bg-red-500 text-white text-sm font-medium px-2 py-1 rounded">
                        Sale
                      </span>
                    )}
                  </div>

                  {/* SKU and Stock Status */}
                  <div className="flex items-center gap-4 text-sm text-gray-600 mb-6">
                    <span>SKU: {product.sku}</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      product.stockStatus === 'IN_STOCK'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {product.stockStatus === 'IN_STOCK' ? 'In Stock' : 'Out of Stock'}
                    </span>
                  </div>
                </div>

                {/* Short Description */}
                <div className="mb-8">
                  <div
                    className="text-gray-700 leading-relaxed"
                    dangerouslySetInnerHTML={{ __html: product.shortDescription }}
                  />
                </div>

                {/* Variant Selection for Grouped Products */}
                {product.type === 'GROUPED' && product.ProductAttribute && (
                  <div className="mb-8">
                    <ProductVariantSelector
                      productAttributes={product.ProductAttribute}
                      selectedAttributes={{}}
                      onAttributeChange={() => {}}
                    />
                  </div>
                )}

                {/* Categories and Tags */}
                <div className="space-y-4 mb-8">
                  {product.categories && product.categories.length > 0 && (
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Categories</h3>
                      <div className="flex flex-wrap gap-2">
                        {product.categories.map((category: any) => (
                          <Link
                            key={category.id}
                            href={`/categories/${category.slug}`}
                            className="bg-gray-100 text-gray-800 px-3 py-1 rounded-full text-sm hover:bg-gray-200 transition-colors"
                          >
                            {category.name}
                          </Link>
                        ))}
                      </div>
                    </div>
                  )}

                  {product.tags && product.tags.length > 0 && (
                    <div>
                      <h3 className="font-medium text-gray-900 mb-2">Tags</h3>
                      <div className="flex flex-wrap gap-2">
                        {product.tags.map((tag: any) => (
                          <span
                            key={tag.id}
                            className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm"
                          >
                            #{tag.name}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Add to Cart Button */}
                <button
                  className={`w-full py-3 px-6 rounded-lg font-medium transition-colors ${
                    product.stockStatus === 'IN_STOCK'
                      ? 'bg-main-color text-white hover:bg-main-color/90'
                      : 'bg-gray-400 text-white cursor-not-allowed'
                  }`}
                  disabled={product.stockStatus !== 'IN_STOCK'}
                >
                  {product.stockStatus === 'IN_STOCK' ? 'Add to Cart' : 'Out of Stock'}
                </button>
              </div>
            </div>

            {/* Product Description */}
            <div className="mt-16 border-t pt-16">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Product Description</h2>
              <div
                className="prose max-w-none text-gray-700 leading-relaxed"
                dangerouslySetInnerHTML={{ __html: product.description }}
              />
            </div>

            {/* Product Listings */}
            {product.listings && product.listings.length > 0 && (
              <div className="mt-16 border-t pt-16">
                <ProductListings listings={product.listings} />
              </div>
            )}
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading product page:', error)
    notFound()
  }
}
