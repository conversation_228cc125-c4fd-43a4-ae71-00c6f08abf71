export default function ShopLoading() {
  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section Skeleton */}
      <div className="relative bg-gradient-to-br from-main-color/10 via-white to-main-color/5 py-20">
        <div className="relative px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
          <div className="max-w-4xl">
            <div className="h-16 bg-gray-300 rounded-lg mb-6 animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded mb-8 w-3/4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded w-32 mb-8 animate-pulse"></div>
            <div className="flex flex-wrap gap-4">
              {Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="h-8 bg-gray-300 rounded-full w-32 animate-pulse"></div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Categories Grid Skeleton */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <div className="h-10 bg-gray-300 rounded-lg w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded w-96 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {Array.from({ length: 8 }).map((_, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg border border-gray-100">
                <div className="aspect-[4/3] bg-gray-300 rounded-t-2xl animate-pulse"></div>
                <div className="p-6">
                  <div className="h-6 bg-gray-300 rounded mb-2 animate-pulse"></div>
                  <div className="h-4 bg-gray-300 rounded w-24 mb-3 animate-pulse"></div>
                  <div className="h-4 bg-gray-300 rounded w-32 animate-pulse"></div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* Featured Products Skeleton */}
        <section className="bg-gray-50 -mx-4 md:-mx-8 lg:-mx-16 xl:-mx-32 2xl:-mx-64 px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-16">
          <div className="text-center mb-12">
            <div className="h-10 bg-gray-300 rounded-lg w-64 mx-auto mb-4 animate-pulse"></div>
            <div className="h-6 bg-gray-300 rounded w-96 mx-auto mb-4 animate-pulse"></div>
            <div className="h-4 bg-gray-300 rounded w-48 mx-auto animate-pulse"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 mb-12">
            {Array.from({ length: 12 }).map((_, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-lg border border-gray-100">
                <div className="aspect-square bg-gray-300 rounded-t-2xl animate-pulse"></div>
                <div className="p-6">
                  <div className="h-5 bg-gray-300 rounded mb-2 animate-pulse"></div>
                  <div className="h-6 bg-gray-300 rounded w-24 mb-3 animate-pulse"></div>
                  <div className="flex justify-between items-center">
                    <div className="h-4 bg-gray-300 rounded w-20 animate-pulse"></div>
                    <div className="h-6 bg-gray-300 rounded w-16 animate-pulse"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Enhanced Pagination Skeleton */}
          <div className="bg-white rounded-2xl shadow-lg p-6 border border-gray-100">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <div className="h-4 bg-gray-300 rounded w-48 animate-pulse"></div>
              <div className="flex items-center gap-2">
                <div className="h-10 bg-gray-300 rounded-lg w-20 animate-pulse"></div>
                {Array.from({ length: 5 }).map((_, index) => (
                  <div key={index} className="h-10 bg-gray-300 rounded-lg w-10 animate-pulse"></div>
                ))}
                <div className="h-10 bg-gray-300 rounded-lg w-16 animate-pulse"></div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  )
}
