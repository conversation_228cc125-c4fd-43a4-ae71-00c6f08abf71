// Utility functions for generating static params across the application

interface MainCategory {
  id: number
  name: string
  slug: string
  count?: number
  imageUrl?: string
}

interface Subcategory {
  id: number
  name: string
  slug: string
  count?: number
  imageUrl?: string
  parentId: number
}

interface Product {
  id: number
  name: string
  slug: string
}

// Generate all main category static params
export async function generateMainCategoryParams() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300'
    const response = await fetch(`${baseUrl}/api/shop/main-categories`, {
      cache: 'no-store'
    })
    
    if (!response.ok) {
      console.error('Failed to fetch main categories for static params')
      return []
    }
    
    const data = await response.json()
    const categories: MainCategory[] = data.data || data
    
    return categories.map((category) => ({
      mainSlug: category.slug,
    }))
  } catch (error) {
    console.error('Error generating main category static params:', error)
    return []
  }
}

// Generate all subcategory static params (for all main categories)
export async function generateSubcategoryParams() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300'
    
    // First, get all main categories
    const mainResponse = await fetch(`${baseUrl}/api/shop/main-categories`, {
      cache: 'no-store'
    })
    
    if (!mainResponse.ok) {
      console.error('Failed to fetch main categories for subcategory static params')
      return []
    }
    
    const mainData = await mainResponse.json()
    const mainCategories: MainCategory[] = mainData.data || mainData
    
    const params: { mainSlug: string; catSlug: string }[] = []
    
    // For each main category, fetch its subcategories
    for (const mainCategory of mainCategories) {
      try {
        const subResponse = await fetch(
          `${baseUrl}/api/shop/main-categories/${mainCategory.id}/categories`,
          { cache: 'no-store' }
        )
        
        if (!subResponse.ok) {
          console.error(`Failed to fetch subcategories for main category: ${mainCategory.id}`)
          continue
        }
        
        const subData = await subResponse.json()
        const subcategories: Subcategory[] = subData.subcategories || []
        
        for (const subcategory of subcategories) {
          params.push({
            mainSlug: mainCategory.slug,
            catSlug: subcategory.slug,
          })
        }
      } catch (error) {
        console.error(`Error fetching subcategories for main category ${mainCategory.id}:`, error)
        continue
      }
    }
    
    return params
  } catch (error) {
    console.error('Error generating subcategory static params:', error)
    return []
  }
}

// Generate all product static params
export async function generateProductParams() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300'
    const response = await fetch(`${baseUrl}/api/shop/products/names`, {
      cache: 'no-store'
    })
    
    if (!response.ok) {
      console.error('Failed to fetch product names for static params')
      return []
    }
    
    const data = await response.json()
    const products: Product[] = data.data || data
    
    return products.map((product) => ({
      prodSlug: product.slug,
    }))
  } catch (error) {
    console.error('Error generating product static params:', error)
    return []
  }
}

// Helper function to validate environment variables
export function validateBuildEnvironment() {
  const requiredEnvVars = [
    'NEXT_PUBLIC_API_BASE_URL',
    'NEXT_PUBLIC_SITE_URL'
  ]
  
  const missing = requiredEnvVars.filter(envVar => !process.env[envVar])
  
  if (missing.length > 0) {
    console.warn(`Missing environment variables for optimal SEO: ${missing.join(', ')}`)
    console.warn('Using default values, but this may affect SEO performance in production.')
  }
  
  return missing.length === 0
}

// Helper function to log build-time statistics
export function logBuildStats(
  mainCategories: number,
  subcategories: number,
  products: number
) {
  console.log('🏗️  Build-time SEO Statistics:')
  console.log(`   📁 Main Categories: ${mainCategories}`)
  console.log(`   📂 Subcategories: ${subcategories}`)
  console.log(`   📦 Products: ${products}`)
  console.log(`   📄 Total Pages: ${mainCategories + subcategories + products + 1}`) // +1 for products index
  console.log('✅ All pages will be pre-rendered for optimal SEO')
}
