import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaSeedling } from 'react-icons/fa';
import { GiMedal, GiPlantRoots } from 'react-icons/gi';
import { BiSolidCertification } from 'react-icons/bi';
import Image from "next/image";
import Link from "next/link";

const Footer = () => {
  return (
    <div className="py-24 px-4 md:px-8 lg:px-16 xl:32 2xl:px-64 bg-gray-100 text-sm mt-24">
      {/* TOP */}
      <div className="flex flex-col md:flex-row justify-between gap-24">
        {/* LEFT */}
        <div className="w-full md:w-1/2 lg:w-1/4 flex flex-col gap-8">
          <Link href="/">
            <div className="text-2xl tracking-wide">Cocojojo LLC</div>
          </Link>
          <p>
          Office: 3109 S Main St, Santa Ana, CA 92707
          </p>
          <span className="font-semibold"><EMAIL></span>
          <span className="font-semibold">(949) 610-7164</span>
          <div className="flex gap-6">
            <Image src="/facebook.png" alt="" width={16} height={16} />
            <Image src="/instagram.png" alt="" width={16} height={16} />
            <Image src="/youtube.png" alt="" width={16} height={16} />
            <Image src="/pinterest.png" alt="" width={16} height={16} />
            <Image src="/x.png" alt="" width={16} height={16} />
          </div>
        </div>
        {/* CENTER */}
        <div className="hidden lg:block w-1/2">
          <h1 className="font-medium text-2xl mb-6">Our Story</h1>
          <div className="space-y-6 leading-relaxed">
            <p>
              At Cocojojo, our team believes in high quality beauty products that harness 
              the healing powers of Witch Hazel Rose Toner for Skinpure and natural ingredients.
            </p>
            <p>
              We are committed to delivering honest beauty products that produce real results, 
              enhance your well-being, and sustain the Earth.
            </p>
            <p className="font-medium text-main-color">
              No additives, no funny business. Just 100% pure, organic oils.
            </p>
            <div className="pt-4 flex flex-wrap gap-4">
              <span className="px-4 py-2 bg-white/50 rounded-full text-sm">100% Pure</span>
              <span className="px-4 py-2 bg-white/50 rounded-full text-sm">Organic Certified</span>
              <span className="px-4 py-2 bg-white/50 rounded-full text-sm">Sustainable</span>
              <span className="px-4 py-2 bg-white/50 rounded-full text-sm">Natural Healing</span>
              <span className="px-4 py-2 bg-white/50 rounded-full text-sm">Earth-Friendly</span>
            </div>
          </div>
        </div>
        {/* RIGHT */}
        <div className="w-full md:w-1/2 lg:w-1/4 flex flex-col gap-8">
          <h1 className="font-medium text-lg">OUR CERTIFICATIONS</h1>
          <div className="flex flex-col gap-4">
            <div className="flex items-center gap-3">
              <BiSolidCertification className="text-main-color text-3xl" />
              <span className="text-sm">USDA Certified Organic</span>
            </div>
            <div className="flex items-center gap-3">
              <FaLeaf className="text-main-color text-3xl" />
              <span className="text-sm">Eco-Certified Sustainable</span>
            </div>
            <div className="flex items-center gap-3">
              <FaSeedling className="text-main-color text-3xl" />
              <span className="text-sm">MSDS certification</span>
            </div>
            <div className="flex items-center gap-3">
              <GiPlantRoots className="text-main-color text-3xl" />
              <span className="text-sm">100% Natural Ingredients</span>
            </div>
            <div className="flex items-center gap-3">
              <GiMedal className="text-main-color text-3xl" />
              <span className="text-sm">Bulk orders</span>
            </div>
          </div>
          <p className="text-sm text-main-color font-medium mt-4">
            Every product meets the highest standards of purity and sustainability
          </p>
        </div>
      </div>
      {/* BOTTOM */}
      <div className="flex flex-col md:flex-row items-center justify-between gap-8 mt-16">
        <div className="">© 2024 Organic Pure Oil - All Rights Reserved</div>
        <div className="flex flex-col gap-8 md:flex-row">
          <div className="">
            <span className="text-gray-500 mr-4">Made with love in</span>
            <span className="font-medium">California, USA</span>
          </div>
          <div className="">
            <span className="text-gray-500 mr-4">100%</span>
            <span className="font-medium">Pure & Natural</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;



