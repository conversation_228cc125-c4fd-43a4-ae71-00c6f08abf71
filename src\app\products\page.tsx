import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { shopApi } from '@/services/api'

interface ProductsPageProps {
  searchParams: { page?: string }
}

// Generate metadata for SEO
export const metadata: Metadata = {
  title: 'All Products - Premium Quality | Your Store',
  description: 'Browse our complete collection of premium products. High-quality items with fast shipping and excellent customer service.',
  keywords: ['products', 'shop', 'buy online', 'premium quality', 'e-commerce'],
  openGraph: {
    title: 'All Products - Premium Quality',
    description: 'Browse our complete collection of premium products.',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'All Products - Premium Quality',
    description: 'Browse our complete collection of premium products.',
  },
  alternates: {
    canonical: '/products',
  },
}

export default async function ProductsPage({ searchParams }: ProductsPageProps) {
  const page = parseInt(searchParams.page || '1', 10)
  const limit = 20

  try {
    // Get all products with pagination
    const productsData = await shopApi.getAllProducts(page, limit)
    const products = productsData.data || []
    const pagination = productsData.pagination || { total: 0, page: 1, limit: 20 }

    // Calculate pagination info
    const totalPages = Math.ceil(pagination.total / pagination.limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    // JSON-LD structured data
    const jsonLd = {
      '@context': 'https://schema.org',
      '@type': 'CollectionPage',
      name: 'All Products',
      description: 'Browse our complete collection of premium products',
      url: '/products',
      breadcrumb: {
        '@type': 'BreadcrumbList',
        itemListElement: [
          {
            '@type': 'ListItem',
            position: 1,
            item: { '@id': '/', name: 'Home' },
          },
          {
            '@type': 'ListItem',
            position: 2,
            item: { '@id': '/products', name: 'Products' },
          },
        ],
      },
      mainEntity: {
        '@type': 'ItemList',
        numberOfItems: pagination.total,
        itemListElement: products.map((product: any, index: number) => ({
          '@type': 'ListItem',
          position: index + 1,
          item: {
            '@type': 'Product',
            name: product.name,
            url: `/products/${product.slug}`,
            image: product.imageUrl,
            offers: {
              '@type': 'Offer',
              price: product.salePrice || product.price,
              priceCurrency: 'USD',
              availability: product.inStock ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock',
            },
          },
        })),
      },
    }

    return (
      <>
        {/* JSON-LD structured data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(jsonLd) }}
        />

        <div className="min-h-screen bg-white">
          {/* Hero Section */}
          <div className="relative bg-gradient-to-r from-gray-50 to-gray-100 py-16">
            <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64">
              <div className="max-w-4xl">
                <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
                  All Products
                </h1>
                <p className="text-xl text-gray-600 mb-6">
                  Discover our complete collection of premium products with {pagination.total} items
                </p>
                <nav className="text-sm text-gray-500">
                  <Link href="/" className="hover:text-gray-700">Home</Link>
                  <span className="mx-2">/</span>
                  <span className="text-gray-900">Products</span>
                </nav>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
            {/* Products Grid */}
            <section>
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-900">
                  Products ({pagination.total})
                </h2>
                <div className="text-sm text-gray-500">
                  Page {page} of {totalPages}
                </div>
              </div>

              {products.length > 0 ? (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-12">
                    {products.map((product: any) => (
                      <Link
                        key={product.id}
                        href={`/products/${product.slug}`}
                        className="group bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow duration-200"
                      >
                        <div className="aspect-square relative overflow-hidden rounded-t-lg">
                          {product.imageUrl ? (
                            <Image
                              src={product.imageUrl}
                              alt={product.name}
                              fill
                              className="object-cover group-hover:scale-105 transition-transform duration-200"
                            />
                          ) : (
                            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                              <span className="text-gray-400 text-4xl">📦</span>
                            </div>
                          )}
                        </div>
                        <div className="p-4">
                          <h3 className="font-medium text-gray-900 group-hover:text-main-color transition-colors line-clamp-2">
                            {product.name}
                          </h3>
                          <div className="flex items-center gap-2 mt-2">
                            {product.salePrice ? (
                              <>
                                <span className="text-lg font-semibold text-main-color">
                                  ${product.salePrice.toFixed(2)}
                                </span>
                                <span className="text-sm text-gray-500 line-through">
                                  ${product.price.toFixed(2)}
                                </span>
                              </>
                            ) : (
                              <span className="text-lg font-semibold text-gray-900">
                                ${product.price.toFixed(2)}
                              </span>
                            )}
                          </div>
                          <div className="mt-2">
                            <span className={`text-xs px-2 py-1 rounded-full ${
                              product.inStock
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                            }`}>
                              {product.inStock ? 'In Stock' : 'Out of Stock'}
                            </span>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>

                  {/* Enhanced Pagination */}
                  {totalPages > 1 && (
                    <div className="flex justify-center items-center gap-2 my-8">
                      {/* Previous Button */}
                      {hasPrevPage && (
                        <Link
                          href={`/products?page=${page - 1}`}
                          className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                        >
                          Previous
                        </Link>
                      )}

                      {/* Page Numbers */}
                      {Array.from({ length: Math.min(totalPages, 10) }).map((_, idx) => {
                        let pageNum: number

                        if (totalPages <= 10) {
                          pageNum = idx + 1
                        } else {
                          // Show pages around current page
                          const start = Math.max(1, page - 4)
                          const end = Math.min(totalPages, start + 9)
                          pageNum = start + idx

                          if (pageNum > end) return null
                        }

                        return (
                          <Link
                            key={pageNum}
                            href={`/products?page=${pageNum}`}
                            className={`px-3 py-2 rounded-lg transition-colors ${
                              pageNum === page
                                ? 'bg-main-color text-white'
                                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                            }`}
                          >
                            {pageNum}
                          </Link>
                        )
                      })}

                      {/* Next Button */}
                      {hasNextPage && (
                        <Link
                          href={`/products?page=${page + 1}`}
                          className="px-4 py-2 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
                        >
                          Next
                        </Link>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <div className="text-center py-12">
                  <div className="text-gray-400 text-6xl mb-4">📦</div>
                  <h3 className="text-xl font-medium text-gray-900 mb-2">No products found</h3>
                  <p className="text-gray-600">
                    There are currently no products available.
                  </p>
                </div>
              )}
            </section>
          </div>
        </div>
      </>
    )
  } catch (error) {
    console.error('Error loading products page:', error)

    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 text-6xl mb-4">⚠️</div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Error Loading Products</h1>
          <p className="text-gray-600">
            We're having trouble loading the products. Please try again later.
          </p>
        </div>
      </div>
    )
  }
}
